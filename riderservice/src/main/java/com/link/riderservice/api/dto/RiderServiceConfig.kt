package com.link.riderservice.api.dto

/**
 * 仪表盘配置
 * @property isSupportDvr 是否支持DVR
 * @property isSupportNavi 是否支持导航
 * @property isSupportScreenNavi 是否支持投屏导航
 * @property isSupportWeather 是否支持天气
 * @property isSupportNotification 是否支持通知
 * @property isSupportCircularScreen 是否支持圆屏
 * @property isSupportCruise 是否支持巡航
 * @property isSupportMirror 是否支持镜像
 */
data class RiderServiceConfig(
    val isSupportDvr: <PERSON>olean,
    val isSupportNavi: <PERSON>olean,
    val isSupportScreenNavi: <PERSON><PERSON>an,
    val isSupportWeather: <PERSON><PERSON><PERSON>,
    val isSupportNotification: <PERSON><PERSON><PERSON>,
    val isSupportCircularScreen: <PERSON>olean,
    val isSupportCruise:Boolean,
    val isSupportMirror:Boolean
)
