// Copyright 2014 Google Inc. All Rights Reserved.

#include "util/common.h"
#include "ProtocolEndpointBase.h"

void ProtocolEndpointBase::onChannelOpened(uint8_t channelId, uint32_t extraMessage) {
    mChannelId = channelId;
    mConnected = true;
    mExtraMessage = extraMessage;
}

bool ProtocolEndpointBase::mayOpenChannel(uint8_t channelId) {
    /* Can only accept one connection by default. */
    return !mConnected;
}

bool ProtocolEndpointBase::onChannelClosed(uint8_t channelId) {
    if (!mConnected) {
        return false;
    }
    mConnected = false;
    return true;
}

bool ProtocolEndpointBase::discoverService(const Service &sdr) {
    return false;
}

void ProtocolEndpointBase::addDiscoveryInfo(ServiceDiscoveryResponse *sdr) {

}

bool ProtocolEndpointBase::isExtraMessageExist(MediaMessageExistanceSetup message) {
    LOGD("extraMessage:%d\n", mExtraMessage);
    return message & mExtraMessage;
}


