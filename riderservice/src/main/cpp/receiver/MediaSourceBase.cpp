// Copyright 2014 Google Inc. All Rights Reserved.

#include "MediaSourceBase.h"

int
MediaSourceBase::routeMessage(uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg) {
    int ret = STATUS_UNEXPECTED_MESSAGE;
    uint8_t *ptr = (uint8_t *) msg->raw() + sizeof(uint16_t);
    size_t len = msg->size() - sizeof(uint16_t);
    uint64_t timestamp = 0;
    //LOG("[MediaSource] <- ");

    switch (type) {
        case MEDIA_MESSAGE_CONFIG: {
            LOG("Config\n");
            Config config;
            if (PARSE_PROTO(config, ptr, len)) {
                ret = handleConfig(config);
            }
            break;
        }
        case MEDIA_MESSAGE_ACK: {
            //LOG("ACK\n");
            Ack ack;
            if (PARSE_PROTO(ack, ptr, len)) {
                ret = handleAck(ack);
            }
            break;
        }
        case MEDIA_MESSAGE_START_RESPONSE: {
            LOG("StartResponse\n");
            StartResponse startResponse;
            if (PARSE_PROTO(startResponse, ptr, len)) {
                ret = handleStartResponse(startResponse);
            }
            break;
        }
        case MEDIA_MESSAGE_STOP_RESPONSE: {
            LOG("StopResponse\n");
            StopResponse stopResponse;
            if (PARSE_PROTO(stopResponse, ptr, len)) {
                ret = handleStopResponse(stopResponse);
            }
        }

        case MESSAGE_UNEXPECTED_MESSAGE: {
            LOG("Unexpected Message!!!\n");
            //mRouter->closeChannel(channelId);
            break;
        }
        default: {
            LOG("Unknown Message!!!\n");
            break;
        }
    }
    //LOG("\n");
    return ret;
}

void MediaSourceBase::sendSetup(MediaCodecType type) {
    LOG("[MediaSource] -> Setup\n");
    Setup setup;
    setup.set_type(type);
    IoBuffer buf;
    MessageRouter::marshallProto(MEDIA_MESSAGE_SETUP, setup, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void MediaSourceBase::sendStart(uint32_t config_index, int32_t session_id, int32_t width,
                                int32_t height) {
    LOG("[MediaSource] -> Start\n");
    Start start;
    start.set_session_id(session_id);
    start.set_configuration_index(config_index);
    start.set_configuration_video_width(width);
    start.set_configuration_video_height(height);
    IoBuffer buf;
    MessageRouter::marshallProto(MEDIA_MESSAGE_START, start, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void MediaSourceBase::sendStop() {
    LOG("[MediaSource] -> Stop\n");
    size_t len = sizeof(uint16_t);
    auto *buf = new uint8_t[len];
    WRITE_BE16(buf, MEDIA_MESSAGE_STOP);
    queueOutgoing(buf, len);
    delete[] buf;
}

void MediaSourceBase::sendCodecConfig(void *config, size_t len) {
    LOG("[MediaSource] -> CodecConfig\n");
    auto *buf = new uint8_t[len + sizeof(uint16_t)];
    WRITE_BE16(buf, MEDIA_MESSAGE_CODEC_CONFIG);
    memcpy(buf + sizeof(uint16_t), (uint8_t *) config, len);
    queueOutgoing(buf, len + sizeof(uint16_t));
    delete[] buf;
}

void MediaSourceBase::sendData(uint64_t timeStamp, void *data, size_t len, bool isIDR) {
    //LOG("[MediaSource] -> timeStamp(%lu)\n", timeStamp);
    int32_t bufSize = len + sizeof(uint16_t) + sizeof(uint64_t);
    auto *buf = new uint8_t[bufSize];
    WRITE_BE16(buf, MEDIA_MESSAGE_DATA);
    WRITE_BE64(buf + sizeof(uint16_t), timeStamp);
    memcpy(buf + sizeof(uint16_t) + sizeof(uint64_t), (uint8_t *) data, len);
    queueOutgoing(buf, bufSize);
    delete[] buf;
}
