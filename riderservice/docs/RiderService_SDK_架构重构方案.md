# RiderService SDK 架构重构方案

**版本**: 2.0 | **状态**: 架构设计 | **类型**: 架构重构

---

## 目录

- [当前架构分析](#当前架构分析)
- [架构重构设计](#架构重构设计)
- [包结构重组](#包结构重组)
- [实现细节](#实现细节)
- [迁移策略](#迁移策略)

---

## 当前架构分析

### 当前架构问题

从代码分析可以看出，当前SDK架构存在以下问题：

1. **RiderService类过于庞大**: 863行代码，承担了太多职责
2. **管理器职责不清**: ConnectionManager和MessageManager功能重叠
3. **回调处理分散**: 回调逻辑散布在各个管理器中
4. **依赖关系复杂**: 各组件之间耦合度高
5. **缺乏统一的错误处理**: 错误处理逻辑不一致
6. **WiFi连接模式混乱**: AP模式和P2P模式的处理逻辑分散，缺乏统一管理

### 当前架构图

```mermaid
graph TB
    subgraph Current["当前架构 - 问题"]
        App[Android应用]
        
        subgraph API["API层 - 职责混乱"]
            RiderServiceAPI[RiderService<br/>863行代码<br/>25个方法混合]
            ModuleInject[ModuleInject<br/>简单依赖注入]
        end
        
        subgraph Management["管理层 - 职责重叠"]
            ConnectionMgr[ConnectionManager<br/>连接协调 + 回调分发]
            MessageMgr[MessageManager<br/>消息队列 + 协议处理]
        end
        
        subgraph Connection["连接层 - 分散管理"]
            BleManager[RiderBleManager<br/>BLE连接]
            DisplayManager[DisplayNaviManager<br/>WiFi + 投屏]
        end
        
        subgraph Lower["底层实现"]
            BleImpl[BleManagerImpl]
            P2PManager[SoftP2pManager]
            AutolinkControl[AutolinkControl]
            Transport[Transport]
        end
    end
    
    App --> RiderServiceAPI
    RiderServiceAPI --> ConnectionMgr
    RiderServiceAPI --> MessageMgr
    ConnectionMgr --> BleManager
    ConnectionMgr --> DisplayManager
    MessageMgr --> Transport
    
    classDef problem fill:#d32f2f,stroke:#f44336,stroke-width:2px,color:#ffffff
    classDef api fill:#ff9800,stroke:#f57c00,stroke-width:2px,color:#ffffff
    classDef management fill:#2196f3,stroke:#1976d2,stroke-width:2px,color:#ffffff
    classDef connection fill:#4caf50,stroke:#388e3c,stroke-width:2px,color:#ffffff
    classDef lower fill:#9e9e9e,stroke:#616161,stroke-width:2px,color:#ffffff
    
    class RiderServiceAPI problem
    class ModuleInject api
    class ConnectionMgr,MessageMgr management
    class BleManager,DisplayManager connection
    class BleImpl,P2PManager,AutolinkControl,Transport lower
```

---

## 架构重构设计

### 新架构原则

1. **单一职责**: 每个组件只负责一个明确的功能域
2. **接口隔离**: 按功能分组接口，客户端按需实现
3. **依赖倒置**: 高层模块不依赖低层模块，都依赖抽象
4. **开闭原则**: 对扩展开放，对修改关闭
5. **模式分离**: WiFi连接的AP模式和P2P模式独立实现，统一管理

### WiFi双模式架构设计

**AP客户端模式 (WIFI_AP_CLIENT)**:
- 仪表端作为WiFi热点，手机作为客户端连接
- 使用WiFiClientManager管理连接
- 通过BLE获取热点SSID和密码
- 适用于稳定的点对点连接

**P2P直连模式 (WIFI_P2P)**:
- 使用WiFi Direct技术直接连接
- 使用SoftP2pManager管理连接
- 无需预共享密钥，自动协商连接
- 适用于快速建立的临时连接

**统一管理**:
- ConnectionCore统一管理两种模式的切换
- 根据WifiConnectionMode枚举选择连接方式
- 提供统一的连接状态回调和错误处理

### 新架构图

```mermaid
graph TB
    App[Android应用]

    subgraph API["API层 - 职责清晰"]
        RiderServiceNew[RiderService<br/>主入口<br/>生命周期管理]

        subgraph Managers["功能管理器"]
            ConnectionAPI[ConnectionManager<br/>连接管理]
            MessagingAPI[MessagingManager<br/>消息通信]
            NavigationAPI[NavigationManager<br/>导航控制]
            ProjectionAPI[ProjectionManager<br/>投屏控制]
            ConfigAPI[ConfigManager<br/>配置管理]
        end
    end

    subgraph Callback["回调层 - 接口隔离"]
        ConnListener[ConnectionListener<br/>连接事件]
        NaviListener[NavigationListener<br/>导航事件]
        ProjListener[ProjectionListener<br/>投屏事件]
        MsgListener[MessageListener<br/>消息事件]
    end

    subgraph Core["核心层 - 业务逻辑"]
        ConnCore[ConnectionCore<br/>连接核心逻辑]
        MsgCore[MessagingCore<br/>消息核心逻辑]
        NaviCore[NavigationCore<br/>导航核心逻辑]
        ProjCore[ProjectionCore<br/>投屏核心逻辑]
        ConfigCore[ConfigCore<br/>配置核心逻辑]
    end

    subgraph Feature["功能层 - 具体实现"]
        BleFeature[BLE连接功能]

        subgraph WiFiFeatures["WiFi连接功能"]
            WiFiApFeature[WiFi AP客户端模式]
            WiFiP2pFeature[WiFi P2P直连模式]
        end

        ProtocolFeature[协议处理功能]
        DisplayFeature[显示管理功能]
        StorageFeature[存储管理功能]
    end

    subgraph Infrastructure["基础设施层"]
        BleImpl[BleManagerImpl]
        WiFiClientMgr[WiFiClientManager<br/>AP模式实现]
        P2PManager[SoftP2pManager<br/>P2P模式实现]
        AutolinkControl[AutolinkControl]
        Transport[Transport]
        Native[Native层]
    end

    App --> RiderServiceNew
    RiderServiceNew --> ConnectionAPI
    RiderServiceNew --> MessagingAPI
    RiderServiceNew --> NavigationAPI
    RiderServiceNew --> ProjectionAPI
    RiderServiceNew --> ConfigAPI

    ConnectionAPI -.-> ConnListener
    MessagingAPI -.-> MsgListener
    NavigationAPI -.-> NaviListener
    ProjectionAPI -.-> ProjListener

    ConnectionAPI --> ConnCore
    MessagingAPI --> MsgCore
    NavigationAPI --> NaviCore
    ProjectionAPI --> ProjCore
    ConfigAPI --> ConfigCore

    ConnCore --> BleFeature
    ConnCore --> WiFiApFeature
    ConnCore --> WiFiP2pFeature
    MsgCore --> ProtocolFeature
    ProjCore --> DisplayFeature
    ConfigCore --> StorageFeature

    BleFeature --> BleImpl
    WiFiApFeature --> WiFiClientMgr
    WiFiP2pFeature --> P2PManager
    ProtocolFeature --> Transport
    DisplayFeature --> AutolinkControl

    classDef api fill:#1b5e20,stroke:#66bb6a,stroke-width:3px,color:#ffffff
    classDef manager fill:#1565c0,stroke:#42a5f5,stroke-width:2px,color:#ffffff
    classDef listener fill:#4a148c,stroke:#ab47bc,stroke-width:2px,color:#ffffff
    classDef core fill:#e65100,stroke:#ff9800,stroke-width:2px,color:#ffffff
    classDef feature fill:#2e7d32,stroke:#4caf50,stroke-width:2px,color:#ffffff
    classDef infra fill:#424242,stroke:#757575,stroke-width:2px,color:#ffffff

    class RiderServiceNew api
    class ConnectionAPI,MessagingAPI,NavigationAPI,ProjectionAPI,ConfigAPI manager
    class ConnListener,NaviListener,ProjListener,MsgListener listener
    class ConnCore,MsgCore,NaviCore,ProjCore,ConfigCore core
    class BleFeature,WiFiApFeature,WiFiP2pFeature,ProtocolFeature,DisplayFeature,StorageFeature feature
    class BleImpl,WiFiClientMgr,P2PManager,AutolinkControl,Transport,Native infra
```

---

## 包结构重组

### 当前包结构问题

```
com.link.riderservice/
├── api/                    # API层混乱
│   ├── RiderService.kt     # 863行巨型类
│   ├── callback/           # 单一巨型回调接口
│   └── dto/               # 数据传输对象
├── feature/               # 功能层职责不清
│   ├── connection/        # 连接功能
│   ├── messaging/         # 消息功能
│   └── display/          # 显示功能
└── core/                 # 核心工具
    ├── di/               # 依赖注入
    └── utils/            # 工具类
```

### 新包结构设计

```
com.link.riderservice/
├── api/                           # 公开API层
│   ├── RiderService.kt            # 主入口接口
│   ├── manager/                   # 功能管理器接口
│   │   ├── ConnectionManager.kt
│   │   ├── MessagingManager.kt
│   │   ├── NavigationManager.kt
│   │   ├── ProjectionManager.kt
│   │   └── ConfigManager.kt
│   ├── listener/                  # 分组回调接口
│   │   ├── ConnectionListener.kt
│   │   ├── NavigationListener.kt
│   │   ├── ProjectionListener.kt
│   │   └── MessageListener.kt
│   └── dto/                       # 数据传输对象
│       ├── NaviInfo.kt
│       ├── NotificationInfo.kt
│       └── ...
├── internal/                      # 内部实现层
│   ├── core/                      # 核心业务逻辑
│   │   ├── connection/
│   │   │   ├── ConnectionCore.kt
│   │   │   └── ConnectionState.kt
│   │   ├── messaging/
│   │   │   ├── MessagingCore.kt
│   │   │   └── MessageQueue.kt
│   │   ├── navigation/
│   │   │   ├── NavigationCore.kt
│   │   │   └── NaviModeManager.kt
│   │   ├── projection/
│   │   │   ├── ProjectionCore.kt
│   │   │   └── DisplayManager.kt
│   │   └── config/
│   │       ├── ConfigCore.kt
│   │       └── ConfigStorage.kt
│   ├── impl/                      # 管理器实现
│   │   ├── ConnectionManagerImpl.kt
│   │   ├── MessagingManagerImpl.kt
│   │   ├── NavigationManagerImpl.kt
│   │   ├── ProjectionManagerImpl.kt
│   │   └── ConfigManagerImpl.kt
│   └── feature/                   # 功能实现层
│       ├── ble/                   # BLE功能
│       ├── wifi/                  # WiFi功能
│       │   ├── ap/                # AP客户端模式
│       │   │   ├── WiFiApConnectionFeature.kt
│       │   │   └── WiFiClientManager.kt
│       │   ├── p2p/               # P2P直连模式
│       │   │   ├── WiFiP2pConnectionFeature.kt
│       │   │   └── SoftP2pManager.kt
│       │   └── WifiConnectionMode.kt
│       ├── protocol/              # 协议处理
│       ├── display/               # 显示管理
│       └── storage/               # 存储管理
├── libs/                          # 第三方库封装
│   ├── ble/                       # BLE库封装
│   └── glutils/                   # OpenGL工具
└── data/                          # 数据层
    ├── source/                    # 数据源
    └── authorization/             # 授权相关
```

---

## 实现细节

### 1. RiderService主入口重构

**当前问题**: 863行代码，职责混乱

**重构方案**:
```kotlin
// 新的RiderService实现
class RiderServiceImpl private constructor() : RiderService {

    // 管理器实例（延迟初始化）
    override val connection: ConnectionManager by lazy {
        ConnectionManagerImpl(connectionCore)
    }
    override val messaging: MessagingManager by lazy {
        MessagingManagerImpl(messagingCore)
    }
    override val navigation: NavigationManager by lazy {
        NavigationManagerImpl(navigationCore)
    }
    override val projection: ProjectionManager by lazy {
        ProjectionManagerImpl(projectionCore)
    }
    override val config: ConfigManager by lazy {
        ConfigManagerImpl(configCore)
    }

    // 核心组件
    private val connectionCore = ConnectionCore()
    private val messagingCore = MessagingCore()
    private val navigationCore = NavigationCore()
    private val projectionCore = ProjectionCore()
    private val configCore = ConfigCore()

    override fun init(application: Application) {
        // 初始化各个核心组件
        configCore.init(application)
        connectionCore.init(application, messagingCore)
        messagingCore.init(connectionCore)
        navigationCore.init(connectionCore, messagingCore)
        projectionCore.init(application, connectionCore)
    }

    override fun destroy() {
        // 清理资源
        projectionCore.destroy()
        navigationCore.destroy()
        messagingCore.destroy()
        connectionCore.destroy()
        configCore.destroy()
    }

    companion object {
        @Volatile
        private var INSTANCE: RiderService? = null

        fun getInstance(): RiderService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RiderServiceImpl().also { INSTANCE = it }
            }
        }
    }
}
```

### 2. 核心组件设计

**ConnectionCore - 连接核心逻辑**:
```kotlin
internal class ConnectionCore {
    private val bleFeature = BleConnectionFeature()
    private val wifiApFeature = WiFiApConnectionFeature()
    private val wifiP2pFeature = WiFiP2pConnectionFeature()
    private val connectionState = MutableStateFlow(Connection.DISCONNECTED)
    private val listeners = mutableSetOf<WeakReference<ConnectionListener>>()

    fun init(application: Application, messagingCore: MessagingCore) {
        bleFeature.init(application) { status ->
            handleBleStatusChange(status)
        }
        wifiApFeature.init(application) { status ->
            handleWiFiApStatusChange(status)
        }
        wifiP2pFeature.init(application) { status ->
            handleWiFiP2pStatusChange(status)
        }
    }

    suspend fun startBleScan(timeoutMs: Long): Result<List<BleDevice>> {
        return bleFeature.startScan(timeoutMs)
    }

    suspend fun connect(device: BleDevice): Result<Unit> {
        return bleFeature.connect(device)
    }

    suspend fun connectWiFi(mode: WifiConnectionMode): Result<Unit> {
        return when (mode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> wifiApFeature.connect()
            WifiConnectionMode.WIFI_P2P -> wifiP2pFeature.connect()
        }
    }

    fun requestWifiInfo(mode: WifiConnectionMode, isReset: Boolean = true) {
        // 根据模式请求WiFi信息
        when (mode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> wifiApFeature.requestApInfo(isReset)
            WifiConnectionMode.WIFI_P2P -> wifiP2pFeature.requestP2pInfo(isReset)
        }
    }

    private fun handleBleStatusChange(status: BleStatus) {
        updateConnectionState()
        notifyListeners { it.onConnectStatusChange(connectionState.value) }
    }

    private fun handleWiFiApStatusChange(status: WiFiApStatus) {
        updateConnectionState()
        notifyListeners { it.onWifiState(status.isConnected) }
    }

    private fun handleWiFiP2pStatusChange(status: WiFiP2pStatus) {
        updateConnectionState()
        // P2P特有的回调处理
    }

    private fun notifyListeners(action: (ConnectionListener) -> Unit) {
        listeners.removeAll { it.get() == null }
        listeners.forEach { ref ->
            ref.get()?.let(action)
        }
    }
}
```

**MessagingCore - 消息核心逻辑**:
```kotlin
internal class MessagingCore {
    private val messageQueue = MessageQueue()
    private val protocolFeature = ProtocolFeature()
    private val listeners = mutableSetOf<WeakReference<MessageListener>>()

    fun init(connectionCore: ConnectionCore) {
        protocolFeature.init { message ->
            handleIncomingMessage(message)
        }
    }

    fun sendNaviInfo(naviInfo: NaviInfo): Boolean {
        return try {
            val protocolMessage = protocolFeature.encodeNaviInfo(naviInfo)
            messageQueue.enqueue(protocolMessage)
            notifyListeners { it.onMessageSent("NaviInfo", true) }
            true
        } catch (e: Exception) {
            notifyListeners { it.onMessageError("Failed to send NaviInfo: ${e.message}") }
            false
        }
    }

    fun sendNotificationInfo(notificationInfo: NotificationInfo): Boolean {
        return try {
            val protocolMessage = protocolFeature.encodeNotificationInfo(notificationInfo)
            messageQueue.enqueue(protocolMessage)
            notifyListeners { it.onMessageSent("NotificationInfo", true) }
            true
        } catch (e: Exception) {
            notifyListeners { it.onMessageError("Failed to send NotificationInfo: ${e.message}") }
            false
        }
    }

    private fun handleIncomingMessage(message: Any) {
        // 处理接收到的消息
        when (message) {
            is NaviModeChangeMessage -> {
                // 通知导航核心处理模式变化
            }
            is ConfigChangeMessage -> {
                // 通知配置核心处理配置变化
            }
        }
    }
}
```

### 3. 管理器实现

**ConnectionManagerImpl**:
```kotlin
internal class ConnectionManagerImpl(
    private val connectionCore: ConnectionCore
) : ConnectionManager {

    override suspend fun startBleScan(timeoutMs: Long): Result<List<BleDevice>> {
        return connectionCore.startBleScan(timeoutMs)
    }

    override suspend fun connect(device: BleDevice): Result<Unit> {
        return connectionCore.connect(device)
    }

    override suspend fun disconnect(): Result<Unit> {
        return connectionCore.disconnect()
    }

    override fun isConnected(): Boolean {
        return connectionCore.isConnected()
    }

    override fun getCurrentDevice(): BleDevice? {
        return connectionCore.getCurrentDevice()
    }

    override fun getConnectionStatus(): Connection {
        return connectionCore.getConnectionStatus()
    }

    override fun addConnectionListener(listener: ConnectionListener) {
        connectionCore.addListener(listener)
    }

    override fun removeConnectionListener(listener: ConnectionListener) {
        connectionCore.removeListener(listener)
    }

    override fun clearAllListeners() {
        connectionCore.clearAllListeners()
    }
}
```

**MessagingManagerImpl**:
```kotlin
internal class MessagingManagerImpl(
    private val messagingCore: MessagingCore
) : MessagingManager {

    override fun sendNaviInfo(naviInfo: NaviInfo): Boolean {
        return messagingCore.sendNaviInfo(naviInfo)
    }

    override fun sendNotificationInfo(notificationInfo: NotificationInfo): Boolean {
        return messagingCore.sendNotificationInfo(notificationInfo)
    }

    override fun sendWeatherInfo(weatherInfo: WeatherInfo): Boolean {
        return messagingCore.sendWeatherInfo(weatherInfo)
    }

    override fun sendLaneInfo(laneInfo: LaneInfo): Boolean {
        return messagingCore.sendLaneInfo(laneInfo)
    }

    override fun sendNaviRoute(naviRoute: NaviRoute): Boolean {
        return messagingCore.sendNaviRoute(naviRoute)
    }

    override fun sendNaviCross(naviCross: NaviCross): Boolean {
        return messagingCore.sendNaviCross(naviCross)
    }

    override fun sendNaviText(naviText: NaviText): Boolean {
        return messagingCore.sendNaviText(naviText)
    }

    override fun sendGpsSignal(gpsSignal: GpsSignal): Boolean {
        return messagingCore.sendGpsSignal(gpsSignal)
    }

    override fun sendArriveDestination(): Boolean {
        return messagingCore.sendArriveDestination()
    }

    override fun sendNaviStart(): Boolean {
        return messagingCore.sendNaviStart()
    }

    override fun sendNaviStop(): Boolean {
        return messagingCore.sendNaviStop()
    }

    override fun sendAutoLinkConnect(autoLinkConnect: AutoLinkConnect): Boolean {
        return messagingCore.sendAutoLinkConnect(autoLinkConnect)
    }

    override fun getMessageQueueSize(): Int {
        return messagingCore.getQueueSize()
    }

    override fun clearMessageQueue() {
        messagingCore.clearQueue()
    }

    override fun addMessageListener(listener: MessageListener) {
        messagingCore.addListener(listener)
    }

    override fun removeMessageListener(listener: MessageListener) {
        messagingCore.removeListener(listener)
    }
}
```

---

## 迁移策略

### 阶段1: 基础架构搭建 (1周)

**目标**: 建立新的包结构和核心接口

**任务**:
1. 创建新的包结构
2. 定义所有管理器接口
3. 定义分组回调接口
4. 创建核心组件骨架

**文件变更**:
```
新增:
├── api/manager/               # 管理器接口
├── api/listener/              # 回调接口
├── internal/core/             # 核心组件
└── internal/impl/             # 管理器实现

保持:
├── api/dto/                   # 现有数据结构
├── feature/                   # 现有功能实现
└── libs/                      # 现有库封装
```

### 阶段2: 核心组件实现 (1周)

**目标**: 实现核心业务逻辑组件

**任务**:
1. 实现ConnectionCore
2. 实现MessagingCore
3. 实现NavigationCore
4. 实现ProjectionCore
5. 实现ConfigCore

**重构重点**:
- 将现有ConnectionManager的逻辑拆分到ConnectionCore
- 将现有MessageManager的逻辑拆分到MessagingCore
- 保持现有功能不变，只是重新组织

### 阶段3: 管理器实现 (1周)

**目标**: 实现新的管理器接口

**任务**:
1. 实现ConnectionManagerImpl
2. 实现MessagingManagerImpl
3. 实现NavigationManagerImpl
4. 实现ProjectionManagerImpl
5. 实现ConfigManagerImpl

**适配策略**:
- 管理器作为核心组件的门面
- 保持API接口的简洁性
- 处理异步操作和错误处理

### 阶段4: RiderService重构 (1周)

**目标**: 重构主入口类

**任务**:
1. 创建新的RiderServiceImpl
2. 集成所有管理器
3. 实现生命周期管理
4. 保持向后兼容

**兼容性策略**:
```kotlin
// 保持现有API可用，内部委托给新实现
class RiderService {
    // 新API
    val connection: ConnectionManager get() = impl.connection
    val messaging: MessagingManager get() = impl.messaging

    // 旧API兼容
    @Deprecated("Use connection.startBleScan() instead")
    fun startBleScan() {
        // 委托给新实现
        GlobalScope.launch {
            connection.startBleScan()
        }
    }

    @Deprecated("Use messaging.sendNaviInfo() instead")
    fun sendMessageToRiderService(message: RiderMessage) {
        when (message) {
            is NaviInfo -> messaging.sendNaviInfo(message)
            is NotificationInfo -> messaging.sendNotificationInfo(message)
            // ... 其他消息类型
        }
    }
}
```

### 阶段5: 测试和优化 (1周)

**目标**: 确保重构后的稳定性

**任务**:
1. 单元测试覆盖
2. 集成测试验证
3. 性能测试对比
4. 文档更新

**测试策略**:
- 对每个核心组件编写单元测试
- 对管理器接口编写集成测试
- 对整体流程编写端到端测试
- 确保新架构性能不低于原架构

### 风险控制

1. **向后兼容**: 保持现有API可用，逐步迁移
2. **渐进重构**: 分模块重构，降低风险
3. **功能验证**: 每个阶段都要验证功能完整性
4. **性能监控**: 确保重构不影响性能

### 成功标准

1. **代码质量**: 每个类职责单一，代码行数合理
2. **架构清晰**: 依赖关系清晰，层次分明
3. **接口隔离**: 回调接口按功能分组
4. **易于扩展**: 新功能可以轻松添加
5. **向后兼容**: 现有客户端代码无需修改

---

## 总结

这个架构重构方案解决了当前SDK的主要问题：

1. **职责分离**: 将863行的RiderService拆分为5个专门的管理器
2. **接口隔离**: 将25个回调方法分组为4个专门的监听器接口
3. **层次清晰**: 建立了API层、核心层、功能层、基础设施层的清晰架构
4. **易于维护**: 每个组件职责单一，便于理解和维护
5. **便于扩展**: 新功能可以通过添加新的管理器和核心组件来实现

通过这个重构，SDK将具有更好的可维护性、可扩展性和可测试性，同时保持与现有代码的兼容性。
```
```
