# RiderService 统一日志管理系统完整指南

## 概述

RiderService 统一日志管理系统是为了更好地管理项目中的日志输出而设计的。该系统提供了灵活的日志控制功能，支持全局和细粒度的日志管理，同时保持了与现有 BLE 模块日志系统的兼容性。

### 核心特性

- **统一管理**：除 BLE 模块外的所有日志都通过统一系统管理
- **灵活控制**：支持全局和标签级别的开关控制
- **级别设置**：支持不同的日志级别设置
- **默认开启**：默认情况下所有日志都是开启的
- **线程安全**：支持多线程环境下的安全使用
- **性能优化**：避免不必要的字符串拼接和处理
- **向后兼容**：不影响现有 BLE 模块的日志输出

### 设计目标

1. **统一管理**：提供一致的日志管理接口
2. **灵活控制**：支持多层级的日志控制策略
3. **性能优先**：最小化对应用性能的影响
4. **易于使用**：提供简洁直观的API
5. **可扩展性**：支持未来功能扩展

## 系统架构

### 架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        应用层                                │
├─────────────────────────────────────────────────────────────┤
│  LogApi (对外接口)          LogExtensions (扩展函数)        │
├─────────────────────────────────────────────────────────────┤
│                      LogManager (核心管理器)                │
├─────────────────────────────────────────────────────────────┤
│  Logger (接口)              LogLevel (枚举)                │
├─────────────────────────────────────────────────────────────┤
│                    Android Log (系统日志)                   │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. LogLevel (日志级别枚举)
- **路径**: `com.link.riderservice.utils.LogLevel`
- **作用**: 定义日志级别常量
- **级别**: VERBOSE(2) → DEBUG(3) → INFO(4) → WARN(5) → ERROR(6) → ASSERT(7)

#### 2. Logger (日志接口)
- **路径**: `com.link.riderservice.utils.Logger`
- **作用**: 定义标准的日志记录接口
- **方法**: v, d, i, w, e (每个都有带异常的重载版本)

#### 3. LogManager (核心管理器)
- **路径**: `com.link.riderservice.utils.LogManager`
- **作用**: 实现日志管理的核心逻辑
- **特性**:
  - 全局日志开关控制
  - 全局日志级别设置
  - 标签特定的开关和级别控制
  - BLE 模块排除机制
  - 线程安全实现

#### 4. LogExtensions (扩展函数)
- **路径**: `com.link.riderservice.utils.LogExtensions`
- **作用**: 提供便捷的日志记录方法
- **功能**:
  - 全局日志函数 (logD, logI, logW, logE 等)
  - Any 类扩展函数 (自动使用类名作为 TAG)

#### 5. LogApi (对外接口)
- **路径**: `com.link.riderservice.api.LogApi`
- **作用**: 为外部应用提供日志控制接口
- **特性**:
  - Java 兼容性 (@JvmStatic)
  - 便捷方法 (enableVerboseMode, enableProductionMode 等)

### 文件结构

```
riderservice/src/main/java/com/link/riderservice/
├── api/
│   ├── LogApi.kt                    # 对外日志控制API
│   └── RiderService.kt              # 主服务类（已集成日志系统）
├── utils/
│   ├── LogLevel.kt                  # 日志级别枚举
│   ├── Logger.kt                    # 日志接口定义
│   ├── LogManager.kt                # 核心日志管理器
│   ├── LogExtensions.kt             # 扩展函数
│   └── CrashHandler.kt              # 崩溃处理器（已迁移）
└── docs/
    └── RiderService_日志系统完整指南.md  # 本文档
```

## 快速开始

### 1. 基本使用

```kotlin
import com.link.riderservice.utils.*

class MyActivity {
    companion object {
        private const val TAG = "MyActivity"
    }
    
    private fun example() {
        // 方式1：使用全局函数
        logD(TAG, "这是一条调试信息")
        logI(TAG, "这是一条普通信息")
        logW(TAG, "这是一条警告信息")
        logE(TAG, "这是一条错误信息", exception)
        
        // 方式2：使用扩展函数（自动使用类名作为TAG）
        logD("这是一条调试信息")
        logI("这是一条普通信息")
        logW("这是一条警告信息")
        logE("这是一条错误信息", exception)
    }
}
```

### 2. 应用初始化配置

```kotlin
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        configureLogging()
    }
    
    private fun configureLogging() {
        if (BuildConfig.DEBUG) {
            // 开发环境：启用所有日志
            LogApi.enableVerboseMode()
        } else {
            // 生产环境：只显示警告和错误
            LogApi.enableProductionMode()
        }
    }
}
```

### 3. 系统初始化（SDK内部）

```kotlin
// 在 RiderService.init() 中
private fun initLogSystem() {
    if (BuildConfig.DEBUG) {
        LogManager.enableLog()
        LogManager.setLogLevel(LogLevel.VERBOSE)
    } else {
        LogManager.enableLog()
        LogManager.setLogLevel(LogLevel.WARN)
    }
}
```

## 详细使用指南

### 全局控制

```kotlin
// 禁用所有日志
LogManager.disableLog()

// 启用所有日志
LogManager.enableLog()

// 设置全局日志级别为ERROR（只显示错误日志）
LogManager.setLogLevel(LogLevel.ERROR)

// 设置全局日志级别为DEBUG（显示DEBUG及以上级别）
LogManager.setLogLevel(LogLevel.DEBUG)

// 检查当前状态
val isEnabled = LogManager.isLogEnabled()
val currentLevel = LogManager.getLogLevel()
```

### 标签特定控制

```kotlin
// 为特定标签禁用日志
LogManager.disableLogForTag("MyActivity")

// 为特定标签启用日志
LogManager.enableLogForTag("MyActivity")

// 为特定标签设置日志级别
LogManager.setLogLevelForTag("MyActivity", LogLevel.WARN)

// 移除特定标签的设置（恢复全局设置）
LogManager.removeTagSettings("MyActivity")

// 清除所有标签设置
LogManager.clearAllTagSettings()
```

### 便捷API方法

```kotlin
// 使用LogApi的便捷方法
LogApi.enableVerboseMode()      // 启用详细模式
LogApi.enableProductionMode()   // 启用生产模式
LogApi.setLogLevel(LogLevel.INFO)
LogApi.setLogLevelForTag("NetworkManager", LogLevel.DEBUG)
```

## 日志级别说明

| 级别 | 值 | 描述 | 使用场景 |
|------|----|----- |----------|
| VERBOSE | 2 | 详细信息 | 开发调试时的详细信息 |
| DEBUG | 3 | 调试信息 | 开发调试时的一般信息 |
| INFO | 4 | 一般信息 | 正常运行时的重要信息 |
| WARN | 5 | 警告信息 | 潜在的问题或异常情况 |
| ERROR | 6 | 错误信息 | 错误和异常 |
| ASSERT | 7 | 断言 | 严重错误 |

## API参考

### LogManager 主要方法

#### 全局控制
- `enableLog()` - 启用日志
- `disableLog()` - 禁用日志
- `isLogEnabled()` - 检查是否启用
- `setLogLevel(level: LogLevel)` - 设置全局级别
- `getLogLevel()` - 获取当前级别

#### 标签控制
- `enableLogForTag(tag: String)` - 为标签启用日志
- `disableLogForTag(tag: String)` - 为标签禁用日志
- `setLogLevelForTag(tag: String, level: LogLevel)` - 设置标签级别
- `removeTagSettings(tag: String)` - 移除标签设置
- `clearAllTagSettings()` - 清除所有标签设置

### LogExtensions 扩展函数

#### 全局函数
- `logV(tag, message)` / `logV(tag, message, throwable)`
- `logD(tag, message)` / `logD(tag, message, throwable)`
- `logI(tag, message)` / `logI(tag, message, throwable)`
- `logW(tag, message)` / `logW(tag, message, throwable)`
- `logE(tag, message)` / `logE(tag, message, throwable)`

#### Any扩展函数
- `Any.logV(message)` / `Any.logV(message, throwable)`
- `Any.logD(message)` / `Any.logD(message, throwable)`
- `Any.logI(message)` / `Any.logI(message, throwable)`
- `Any.logW(message)` / `Any.logW(message, throwable)`
- `Any.logE(message)` / `Any.logE(message, throwable)`

### LogApi 便捷方法
- `enableVerboseMode()` - 启用详细模式（VERBOSE级别）
- `enableProductionMode()` - 启用生产模式（WARN级别）
- `setLogLevel(level: LogLevel)` - 设置全局日志级别
- `setLogLevelForTag(tag: String, level: LogLevel)` - 设置标签级别

## 迁移指南

### 从原始Android Log迁移

**原始代码：**
```kotlin
import android.util.Log

private const val TAG = "MyClass"

Log.d(TAG, "调试信息")
Log.i(TAG, "普通信息")
Log.w(TAG, "警告信息")
Log.e(TAG, "错误信息", throwable)
```

**迁移后：**
```kotlin
import com.link.riderservice.utils.*

private const val TAG = "MyClass"

logD(TAG, "调试信息")
logI(TAG, "普通信息")
logW(TAG, "警告信息")
logE(TAG, "错误信息", throwable)

// 或者使用扩展函数（推荐）
logD("调试信息")
logI("普通信息")
logW("警告信息")
logE("错误信息", throwable)
```

### 批量替换脚本

可以使用以下正则表达式进行批量替换：

1. **替换import语句**
   - 查找：`import android.util.Log`
   - 替换：`import com.link.riderservice.utils.*`

2. **替换日志调用**
   - 查找：`Log\.([vdiwea])\(`
   - 替换：`log${1.toUpperCase()}(`

### 迁移步骤

1. **导入替换**：更新import语句
2. **调用替换**：更新日志调用方法
3. **配置初始化**：在Application中配置日志系统
4. **测试验证**：确保日志输出正常
5. **逐步优化**：使用扩展函数简化代码

## 最佳实践与示例

### 最佳实践

1. **使用合适的日志级别**：
   - VERBOSE: 详细的调试信息
   - DEBUG: 一般的调试信息
   - INFO: 重要的运行信息
   - WARN: 警告信息
   - ERROR: 错误信息

2. **使用扩展函数**：
   ```kotlin
   // 推荐：自动使用类名作为TAG
   logD("用户点击了按钮")

   // 而不是
   logD("MainActivity", "用户点击了按钮")
   ```

3. **在应用初始化时配置**：
   ```kotlin
   class MyApplication : Application() {
       override fun onCreate() {
           super.onCreate()
           configureLogging()
       }
   }
   ```

4. **为生产环境优化**：
   ```kotlin
   if (!BuildConfig.DEBUG) {
       LogApi.enableProductionMode()
   }
   ```

### 示例场景

#### 场景1：开发调试
```kotlin
// 开启所有日志用于调试
LogApi.enableVerboseMode()
```

#### 场景2：生产环境
```kotlin
// 只保留警告和错误日志
LogApi.enableProductionMode()
```

#### 场景3：特定模块调试
```kotlin
// 全局只显示错误，但ConnectionManager显示所有
LogManager.setLogLevel(LogLevel.ERROR)
LogManager.setLogLevelForTag("ConnectionManager", LogLevel.VERBOSE)
```

#### 场景4：关闭特定模块日志
```kotlin
// 关闭某个噪音较大的模块的日志
LogManager.disableLogForTag("NoisyModule")
```

#### 场景5：临时启用详细日志
```kotlin
LogApi.enableVerboseMode()
// 调试完成后
LogApi.enableProductionMode()
```

## 技术细节

### 工作流程

#### 1. 日志记录流程
```
应用代码调用
    ↓
logD("TAG", "message")
    ↓
LogManager.d(tag, message)
    ↓
shouldLog(tag, LogLevel.DEBUG)
    ↓
检查：BLE标签? → 直接输出
    ↓
检查：全局开关? → 是否启用
    ↓
检查：标签开关? → 标签特定设置
    ↓
检查：日志级别? → 级别比较
    ↓
Android Log.d(tag, message)
```

#### 2. 控制流程
```
外部应用/内部代码
    ↓
LogApi.setLogLevel(LogLevel.INFO)
    ↓
LogManager.setLogLevel(level)
    ↓
更新 currentLogLevel
    ↓
影响后续日志输出判断
```

### 关键特性

#### 1. BLE 模块排除机制
```kotlin
private val bleExcludeTags = setOf(
    "BleManager",
    "BleManagerHandler",
    "BleServerManager",
    "BluetoothLeScannerCompat",
    "ScanCallback",
    "ConnectionObserver",
    "BondingObserver"
)

private fun isBleTag(tag: String): Boolean {
    return bleExcludeTags.any { tag.contains(it, ignoreCase = true) }
}
```

**说明**：以下标签的日志不受此管理器控制，会正常输出：
- BleManager
- BleManagerHandler
- BleServerManager
- BluetoothLeScannerCompat
- ScanCallback
- ConnectionObserver
- BondingObserver

#### 2. 线程安全设计
```kotlin
@Volatile
private var isLogEnabled: Boolean = true

@Volatile
private var currentLogLevel: LogLevel = LogLevel.VERBOSE

private val tagEnabledMap = ConcurrentHashMap<String, Boolean>()
private val tagLevelMap = ConcurrentHashMap<String, LogLevel>()
```

#### 3. 性能优化
```kotlin
private fun shouldLog(tag: String, level: LogLevel): Boolean {
    // 快速路径：BLE 标签直接通过
    if (isBleTag(tag)) return true

    // 快速路径：全局禁用直接返回
    if (!isLogEnabled) return false

    // 其他检查...
}
```

## 扩展性

### 1. 添加新的日志级别
在 `LogLevel` 枚举中添加新的级别常量。

### 2. 添加新的输出目标
实现新的 `Logger` 接口，可以输出到文件、网络等。

### 3. 添加日志格式化
在 `LogManager` 中添加格式化逻辑。

### 4. 添加日志过滤
扩展 `shouldLog` 方法的过滤逻辑。

## 注意事项

1. **性能考虑**：日志检查在输出前进行，避免了不必要的字符串拼接
2. **线程安全**：所有操作都是线程安全的，可以在多线程环境中使用
3. **BLE模块**：BLE相关的日志不受此系统控制，保持原有行为
4. **默认行为**：系统默认启用所有级别的日志
5. **构建优化**：建议在生产环境中设置较高的日志级别以减少日志输出
6. **向后兼容**：现有代码可以逐步迁移，不会影响现有功能
7. **性能影响**：日志检查在输出前进行，避免了不必要的字符串操作

## 故障排除

### 常见问题

1. **日志没有输出**
   - 检查全局日志开关是否启用
   - 检查日志级别设置是否正确
   - 确认标签没有被单独禁用

2. **BLE日志被意外控制**
   - 确认标签名称是否包含BLE相关关键词
   - 检查BLE排除列表是否完整

3. **性能问题**
   - 在生产环境中使用较高的日志级别
   - 避免在循环中进行大量日志输出

### 调试技巧

1. **检查当前配置**：
   ```kotlin
   logD("LogSystem", "Log enabled: ${LogManager.isLogEnabled()}")
   logD("LogSystem", "Current level: ${LogManager.getLogLevel()}")
   ```

2. **临时启用详细日志**：
   ```kotlin
   LogApi.enableVerboseMode()
   // 执行需要调试的代码
   LogApi.enableProductionMode()
   ```
