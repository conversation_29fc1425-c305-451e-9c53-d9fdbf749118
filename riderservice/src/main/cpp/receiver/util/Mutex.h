// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef SUNPLUS_AUTOLINK_PROTOCOL_CORE_UTIL_MUTEX_H
#define SUNPLUS_AUTOLINK_PROTOCOL_CORE_UTIL_MUTEX_H

#include <pthread.h>

// Customize this to be a mutex on your platform
class Mutex {
public:
    Mutex();

    ~Mutex();

    void lock();

    void unlock();

private:
    pthread_mutex_t mMutex;
};

class Autolock {
public:
    Autolock(Mutex *mutex);

    ~Autolock();

private:
    Mutex *mMutex;
};

#endif // SUNPLUS_AUTOLINK_PROTOCOL_CORE_UTIL_MUTEX_H
