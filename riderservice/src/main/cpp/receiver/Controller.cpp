// Copyright 2014 Google Inc. All Rights Reserved.

#include "Controller.h"

int Controller::routeMessage(uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg) {
    int ret = STATUS_UNEXPECTED_MESSAGE;
    uint8_t *ptr = (uint8_t *) msg->raw() + sizeof(uint16_t);
    size_t len = msg->size() - sizeof(uint16_t);
    switch (type) {
        case MESSAGE_VERSION_REQUEST: {
            LOG("[CONTROL] <- VersionRequest\n");
            ret = handleVersionRequest(ptr, len);
            break;
        }
        case MESSAGE_ENCAPSULATED_SSL: {
            LOG("[CONTROL] <- EncapsulatedSsl\n");
            ret = handleEncapsulatedSsl(ptr, len);
            break;
        }
        case MESSAGE_AUTH_COMPLETE: {
            LOG("[CONTROL] <- AuthComplete\n");
            ret = handleAuthComplete();
            break;
        }
        case MESSAGE_SERVICE_DISCOVERY_RESPONSE: {
            LOG("[CONTROL] <- ServiceDiscoversyResponse\n");
            ServiceDiscoveryResponse serviceResp;
            if (PARSE_PROTO(serviceResp, ptr, len)) {
                ret = handleServiceDiscoveryResponse(serviceResp);
            }
            break;
        }
        case MESSAGE_PING_REQUEST: {
            //LOG("PingRequest\n");
            PingRequest req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handlePingRequest(req);
            }
            break;
        }
        case MESSAGE_PING_RESPONSE: {
            //LOG("PingResponse\n");
            PingResponse resp;
            if (PARSE_PROTO(resp, ptr, len)) {
                ret = handlePingResponse(resp);
            }
            break;
        }
        case MESSAGE_EXIT_REQUEST: {
            LOG("[CONTROL] <- ExitRequest\n");
            ExitRequest req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handleExitRequest();
            }
            break;
        }
        case MESSAGE_BYEBYE_REQUEST: {
            LOG("[CONTROL] <- ByeByeRequest\n");
            ByeByeRequest req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handleByeByteRequest(req.reason());
            }
            break;
        }
        case MESSAGE_BYEBYE_RESPONSE: {
            LOG("[CONTROL] <- ByeByeResponse\n");
            ret = handleByeByeResponse();
            break;
        }
        case MESSAGE_FORCE_LANDSCAPE_REQUEST: {
            LOG("[CONTROL] <- ForceLandscapeRequest\n");
            ForceLandscapeRequest req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handleForceLandscapeRequest(req.force());
            }
            break;
        }
        case MESSAGE_SCREEN_ORIENTATION_INQUIRE: {
            LOG("[CONTROL] <- ScreenOrientationInquire\n");
            ret = handleScreenOrientationInqure();
            break;
        }
        case MESSAGE_RUNNING_STATE_INQUIRE: {
            LOG("[CONTROL] <- RunningStateInquire\n");
            ret = handleRunningStateInquire();
            break;
        }
        case MESSAGE_AUTO_ROTATION_REQUEST: {
            LOG("[CONTROL] <- autoRotationRequest!\n");
            AutoRotationRequest req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handleAutoRotationRequest(req.enable());
            }
            break;
        }
        case MESSAGE_READ_RESPONSE: {
            LOG("[CONTROL] <- readResponse!\n");
            ReadResponse req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handleReadResponse(req);
            }
            break;
        }
        case MESSAGE_WRITE_RESPONSE: {
            LOG("[CONTROL] <- writeResponse!\n");
            WriteResponse req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handleWriteResponse(req);
            }
            break;
        }
        case MESSAGE_SCREEN_RESOLUTION_INQUIRE: {
            LOG("[CONTROL] <- ScreenResolutionInquire!\n");
            ret = handleScreenResolutionInquire();
            break;
        }

        case MESSAGE_TIMEDATE_INQUIRE: {
            //LOGD("[CONTROL] <- TimeDateInquire!\n");
            ret = handleTimeDateInquire();
            break;
        }

        case MESSAGE_UNEXPECTED_MESSAGE: {
            LOG("[CONTROL] <- UnexpectedMessage!\n");
            ret = STATUS_SUCCESS;
            break;
        }
        default: {
            LOG("[CONTROL] <- [%d]Unknown Message!!!\n", type);
            break;
        }
    }
    return ret;
}

void Controller::start() {
    //do nothing here!
}


int Controller::handleAutoRotationRequest(bool autoed) {
    mControllerCallbacks->autoRotationRequest(autoed);
    return STATUS_SUCCESS;
}

int Controller::handleVersionRequest(void *msg, size_t len) {
    auto *ptr = (uint8_t *) msg;
    uint16_t major, minor, status;
    READ_BE16(ptr, major);
    READ_BE16(ptr + 2, minor);

    LOG("major=%hu, minor=%hu, ", major, minor);
    if (major == PROTOCOL_MAJOR_VERSION) {
        LOG("result:success\n");
        status = STATUS_SUCCESS;
        mControllerCallbacks->versionResponseCallback(major, minor);
    } else {
        LOG("result:fail\n");
        status = STATUS_NO_COMPATIBLE_VERSION;
    }

    mHealthy = (status == STATUS_SUCCESS);
    sendVersionResponse(status);
    return STATUS_SUCCESS;
}

int Controller::handleEncapsulatedSsl(void *msg, size_t len) {
    //TODO: check SSL certificate
    sendAuthResponse(STATUS_SUCCESS);
    return STATUS_SUCCESS;
}

int Controller::handleAuthComplete() {
    mControllerCallbacks->authCompleteCallback();
    sendServiceDiscoveryRequest();
    return STATUS_SUCCESS;
}

int Controller::handleServiceDiscoveryResponse(ServiceDiscoveryResponse &response) {
    string make = response.make();
    string model = response.model();
    string year = response.year();
    string id = response.vehicle_id();
    string HuIc = response.head_unit_ic();
    string HuMake = response.head_unit_make();
    string HuModel = response.head_unit_model();
    string HuSwBuild = response.head_unit_software_build();
    string HuSwVersion = response.head_unit_software_version();
    string HuSeries = response.head_unit_series();
    string HuMuVersion = response.head_unit_module_version();
    response.head_unit_screen_size();
    response.head_unit_screen_touch_type();
    response.head_unit_screen_width();
    response.head_unit_screen_height();
    int HuCheckSum = response.checksum();
    mDriverPosition = 0;
    mSessionConfiguration = response.session_configuration();
    mControllerCallbacks->serviceDiscoveryResponseCallback(id, make, model, year, HuIc, HuMake,
                                                           HuModel, HuSwBuild, HuSwVersion,
                                                           HuSeries, HuMuVersion, HuCheckSum);
    mRouter->discoverServices(response);
    return STATUS_SUCCESS;
}

int Controller::handlePingRequest(const PingRequest &req) {
    bool bugReport = req.has_bug_report() && req.bug_report();
    // fix delay
    long timestamp = req.timestamp() + 100;
    sendPingResponse(timestamp);
    mControllerCallbacks->pingRequestCallback(req.timestamp(), bugReport);
    return STATUS_SUCCESS;
}

int Controller::handlePingResponse(const PingResponse &resp) {
    mControllerCallbacks->pingResponseCallback(resp.timestamp());
    return STATUS_SUCCESS;
}

int Controller::handleByeByteRequest(ByeByeReason reason) {
    sendByeByeResponse();
    mControllerCallbacks->byeByeRequestCallback(reason);
    return STATUS_SUCCESS;
}

int Controller::handleByeByeResponse() {
    mControllerCallbacks->byeByeResponseCallback();
    return STATUS_SUCCESS;
}

int Controller::handleExitRequest() {
    mControllerCallbacks->exitRequestCallback();
    return STATUS_SUCCESS;
}

int Controller::handleReadResponse(const ReadResponse &resp) {
    return STATUS_SUCCESS;
}

int Controller::handleWriteResponse(const WriteResponse &resp) {
    return STATUS_SUCCESS;
}

int Controller::handleForceLandscapeRequest(bool force) {
    mControllerCallbacks->forceLandscapeRequestCallback(force);
    return STATUS_SUCCESS;
}

int Controller::handleScreenOrientationInqure() {
    mControllerCallbacks->screenOrientationInquire();
    return STATUS_SUCCESS;
}

int Controller::handleScreenResolutionInquire() {
    mControllerCallbacks->screenResolutionInquire();
    return STATUS_SUCCESS;
}

int Controller::handleRunningStateInquire() {
    mControllerCallbacks->runningStateInquire();
    return STATUS_SUCCESS;
}

int Controller::handleTimeDateInquire() {
    mControllerCallbacks->timeDateInquire();
    return STATUS_SUCCESS;
}


void Controller::sendVersionResponse(uint16_t status) {
    LOG("[CONTROL] -> VersionResponse\n");
    size_t len = 4 * sizeof(uint16_t);
    auto *buf = new uint8_t[len];
    WRITE_BE16(buf, MESSAGE_VERSION_RESPONSE);
    WRITE_BE16(buf + 2, PROTOCOL_MAJOR_VERSION);
    WRITE_BE16(buf + 4, PROTOCOL_MINOR_VERSION);
    WRITE_BE16(buf + 6, status);
    queueOutgoingUnencrypted(buf, len);
    delete[] buf;
}

void Controller::sendAuthResponse(int status) {
    LOG("[CONTROL] -> AuthResponse\n");
    AuthResponse resp;
    resp.set_status(status);
    IoBuffer buf;
    MessageRouter::marshallProto(MESSAGE_AUTH_COMPLETE, resp, &buf);
    queueOutgoingUnencrypted(buf.raw(), buf.size());
}

void Controller::sendServiceDiscoveryRequest() {
    LOG("[CONTROL] -> ServiceDiscoverRequest\n");
    ServiceDiscoveryRequest req;
    req.set_manufacturer(mManufacturer);
    req.set_model(mModel);
    req.set_version(mVersion);
    req.set_device_name(mPhoneName);
    req.set_screen_resolution_w(mScreenWidth);
    req.set_screen_resolution_h(mScreenHeight);
    IoBuffer buf;
    MessageRouter::marshallProto(MESSAGE_SERVICE_DISCOVERY_REQUEST, req, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendPingRequest(int64_t timestamp, bool bugReport) {
    //LOG("[CONTROL] -> PingRequest\n");
    PingRequest req;
    req.set_timestamp(timestamp);
    req.set_bug_report(bugReport);
    IoBuffer buf;
    MessageRouter::marshallProto(MESSAGE_PING_REQUEST, req, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendPingResponse(int64_t timestamp) {
    //LOG("[CONTROL] -> PingResponse\n");
    PingResponse resp;
    resp.set_timestamp(timestamp);
    IoBuffer buf;
    MessageRouter::marshallProto(MESSAGE_PING_RESPONSE, resp, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendByeByeRequest(int32_t reason) {
    LOG("[CONTROL] -> ByebyeRequest\n");
    ByeByeRequest request;
    request.set_reason((ByeByeReason) reason);
    IoBuffer buf;
    MessageRouter::marshallProto(MESSAGE_BYEBYE_REQUEST, request, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendByeByeResponse() {
    LOG("[CONTROL] -> ByebyeResponse\n");
    ByeByeResponse response;
    IoBuffer buf;
    MessageRouter::marshallProto(MESSAGE_BYEBYE_RESPONSE, response, &buf);
    queueOutgoing(buf.raw(), buf.size());
}


void Controller::sendExitResponse() {
    LOG("[CONTROL] ->ExitResponse\n");
    ExitResponse response;
    IoBuffer buf;
    MessageRouter::marshallProto(MESSAGE_EXIT_RESPONSE, response, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendNavFocusRequest(int32_t type) {
    LOG("[CONTROL] -> NavFocusRequest\n");
}

void Controller::sendScreenOrientationNotifi(int orientation, int rotation) {
    LOG("[CONTROL] -> OrientationNotification\n");
    ScreenOrientationNotification ntf;
    ntf.set_orientation((ScreenOrientation) orientation);
    ntf.set_rotation((ScreenRotation) rotation);
    IoBuffer buf;
    MessageRouter::marshallProto(MESSAGE_SCREEN_ORIENTATION_NOTIFICATION, ntf, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendUpdateVehicleIdNotifi(string id) {
    LOG("[CONTROL] -> UpdateVehicleIdNotifi\n");
    UpdateVehicleIdNotification ntf;
    ntf.set_vehicle_id(id);
    IoBuffer buf;
    MessageRouter::marshallProto(MESSAGE_UPDATE_VEHICLE_ID_NOTIFICATION, ntf, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendRunningStateNotifi(int state) {
    RunningStateNotification ntf;
    ntf.set_state((RunningState) state);
    IoBuffer buf;
    MessageRouter::marshallProto(MESSAGE_RUNNING_STATE_NOTIFICATION, ntf, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendAutoRotationNotifi(bool isAutoed) {

}

void Controller::sendScreenResolutionNotification(int width, int height, bool isRequired) {
    LOG("[CONTROL] -> sendScreenResolutionNotification\n");
    ScreenResolutionNotification ntf;
    ntf.set_width(static_cast<unsigned int>(width));
    ntf.set_height(static_cast<unsigned int>(height));
    ntf.set_unsolicited(isRequired);
    IoBuffer buf;
    MessageRouter::marshallProto(MESSAGE_SCREEN_RESOLUTION_NOTIFICATION, ntf, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendTimeDateNotification(int year, int month, int day, int hour, int minute,
                                          int second, int nanosecond, int week, int dayOfWeek) {
    //LOG("[CONTROL] -> sendTimeDateNotification\n");
    TimedateNotification ntf;
    ntf.set_year(year);
    ntf.set_month(month);
    ntf.set_day(day);
    ntf.set_hour(hour);
    ntf.set_minute(minute);
    ntf.set_second(second);
    ntf.set_nsecond(nanosecond);
    ntf.set_week(week);
    ntf.set_day_of_week(dayOfWeek);
    IoBuffer buf;
    MessageRouter::marshallProto(MESSAGE_TIMEDATE_NOTIFICATION, ntf, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendForceLandscapeResponse(bool force) {

}








