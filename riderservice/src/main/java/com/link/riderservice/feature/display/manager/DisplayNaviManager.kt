package com.link.riderservice.feature.display.manager

import android.media.projection.MediaProjection
import android.view.Display
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.dto.NaviMode
import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.logging.logE
import com.link.riderservice.core.utils.system.TimeUtils
import com.link.riderservice.feature.connection.transport.DataConnection
import com.link.riderservice.feature.connection.transport.Transport
import com.link.riderservice.feature.connection.transport.tcp.TcpConnection
import com.link.riderservice.feature.connection.transport.tcp.TcpServerConnection
import com.link.riderservice.feature.display.callback.DisplayNaviCallback
import com.link.riderservice.feature.display.protocol.project.Protos
import java.io.IOException
import java.lang.ref.WeakReference


/**
 * <AUTHOR>
 * @date 2022/8/25
 */
internal class DisplayNaviManager(
    private val onTcpConnecting: () -> Unit = {}
) : DataConnection.Callback {
    private var dataConnection: DataConnection? = null
    private var transport: Transport? = null
    private val autolinkControl by lazy {
        AutolinkControl(RiderService.instance.getApplication())
    }

    private val displayNaviCallbacks: MutableList<WeakReference<DisplayNaviCallback>> = mutableListOf()

    private val controlListener: AutolinkControl.OnControlListener =
        object : AutolinkControl.OnControlListener {
            override fun onDisplayInitialized(display: Display?) {
                displayNaviCallbacks.forEach {
                    display?.let { display ->
                        it.get()?.onDisplayInitialized(display)
                    }
                }
            }

            override fun onDisplayReleased(display: Display) {
                displayNaviCallbacks.forEach {
                    it.get()?.onDisplayReleased(display)
                }
            }

            @Deprecated("仪表盘不适用")
            override fun requestLandscape(isLandscape: Boolean) {

            }

            override fun onLoseConnected() {
                logD(TAG, "LoseConnected")
                shutdown()
            }

            override fun onUnrecoverableError(errorCode: Int) {
                logD(TAG, "Received unrecoverable error $errorCode. Shutting down.")
                shutdown()
            }

            override fun onDisconnected() {
                logD(TAG, "onDisconnected")
                shutdown()
            }

            override fun onByeByeRequest(reason: Int) {
                logD(TAG, "received ByeByeRequest with reason $reason")
                shutdown()
            }

            override fun onByeByeResponse() {
                logD(TAG, "received ByeByeResponse")
                shutdown()
            }

            override fun onStatusChanged(status: Int) {
                logD(TAG, "onStatusChanged: $status")
            }

            @Deprecated("仪表盘不适用")
            override fun onRequestAutoRotation(isAutoRotationEnabled: Boolean) {
            }

            override fun stopTransport() {
                transport?.stopTransport()
            }

            override fun onVideoChannelReady() {
                displayNaviCallbacks.forEach {
                    it.get()?.onVideoChannelReady()
                }
            }

            override fun onRequestMediaProjection() {
                displayNaviCallbacks.forEach {
                    it.get()?.onRequestMediaProjection()
                }
            }

            override fun onMirrorStart() {
                displayNaviCallbacks.forEach {
                    it.get()?.onMirrorStart()
                }
            }

            override fun onMirrorStop() {
                displayNaviCallbacks.forEach {
                    it.get()?.onMirrorStop()
                }
            }
        }

    /**
     * 增加回调
     * @param callback 回调
     * @see DisplayNaviCallback
     */
    @Synchronized
    fun addCallback(callback: DisplayNaviCallback) {
        displayNaviCallbacks.add(WeakReference(callback))
    }

    /**
     * 移除回调
     * @param callback 回调
     * @see DisplayNaviCallback
     */
    @Synchronized
    fun removeCallback(callback: DisplayNaviCallback) {
        displayNaviCallbacks.removeIf { it.get() == callback }
    }

    /**
     * 释放资源
     */
    @Synchronized
    fun release() {
        releaseTransport()
        releaseConnection()
        autolinkControl.release()
    }

    private fun releaseTransport() {
        transport?.stopTransport()
        transport = null
    }

    private fun releaseConnection() {
        dataConnection?.shutdown()
        dataConnection = null
    }

    /**
     * 发送再见请求给平台
     */
    fun sendByeByeRequest() {
        if (transport?.isConnected() == true) {
            autolinkControl.sendByeByeRequest()
        }
    }

    /**
     * 开始投屏
     * @return 是否成功
     */
    fun startScreenProjection(): Boolean {
        return if (transport?.isConnected() == true) {
            autolinkControl.setEncoderState(Protos.VIDEO_FOCUS_PROJECTED, Protos.UNKNOWN)
            true
        } else {
            false
        }
    }

    /**
     * 停止投屏
     * @return 是否成功
     */
    fun stopScreenProjection(): Boolean {
        return if (transport?.isConnected() == true) {
            autolinkControl.setEncoderState(Protos.VIDEO_FOCUS_NATIVE, Protos.UNKNOWN)
            true
        } else {
            false
        }
    }

    /**
     * 发送手机屏幕信息给平台
     * @param isLandscape 是否横屏
     * @param rotation 屏幕旋转角度
     */
    fun sendOrientation(isLandscape: Int, rotation: Int) {
        transport?.takeIf { it.isConnected() }?.apply {
            autolinkControl.sendOrientation(isLandscape, rotation)
        }
    }



    fun setNaviMode(naviMode: NaviMode) {
        autolinkControl.setNaviMode(naviMode)
    }

    fun setMediaProjection(mediaProjection: MediaProjection) {
        autolinkControl.setMediaProjection(mediaProjection)
    }


    /**
     * 启动连接建立过程（供ConnectionManager调用）
     */
    fun startConnectionEstablishing() {
        if (dataConnection == null) {
            dataConnection = TcpConnection()
        }

        // 通知TCP连接开始
        onTcpConnecting()

        try {
            dataConnection?.start(this)
        } catch (e: IOException) {
            logE(TAG, "startConnectionEstablishing failed", e)
        }
    }

    /**
     * 启动TCP服务器连接（用于AP客户端模式）
     * 在这种模式下，手机作为TCP服务器，仪表端作为TCP客户端连接
     */
    fun startTcpServerConnection(phoneIpAddress: String) {
        logD(TAG, "Starting TCP server connection for AP client mode")

        // 确保先清理之前的连接
        if (dataConnection != null) {
            logD(TAG, "Cleaning up existing connection")
            dataConnection?.shutdown()
            dataConnection = null
        }

        // 创建专门的TCP服务器连接，传入IP地址
        dataConnection = TcpServerConnection(phoneIpAddress)

        try {
            dataConnection?.start(this)
            logD(TAG, "TCP server connection started successfully")
        } catch (e: IOException) {
            logE(TAG, "Failed to start TCP server connection", e)
            dataConnection = null
            // 通知TCP连接失败
            displayNaviCallbacks.forEach {
                it.get()?.onTcpConnectionFailed(-7) // TCP服务器启动失败
                it.get()?.onDeviceDisconnected()
            }
        }
    }


    /**
     * 开启与平台的交互
     */
    fun start() {
        startControl()
    }

    private fun startControl() {
        autolinkControl.registerListener(controlListener)
        autolinkControl.startConnect(transport)
    }

    override fun onConnected(transport: Transport) {
        logD(TAG, "socket connected")
        <EMAIL> = transport
        logD("connect analysis:", "tcp connect end::${TimeUtils.getCurrentTimeStr()}")
        displayNaviCallbacks.forEach {
            it.get()?.onDeviceConnected()
        }
    }



    @Synchronized
    fun shutdown() {
        releaseTransport()
        releaseConnection()
        autolinkControl.release()
    }

    @Synchronized
    override fun onDisconnected() {
        logD(TAG, "Transport disconnected ${Thread.currentThread().name}")
        displayNaviCallbacks.forEach {
            it.get()?.onDeviceDisconnected()
        }
    }

    override fun requestLockScreenDisplay() {
        autolinkControl.requestLockScreenDisplay()
    }

    fun getAutolinkVersion(): String {
        return autolinkControl.getAutolinkVersion()
    }



    companion object {
        private const val TAG = "DisplayNaviManager"
        private const val DEFAULT_PORT = 30512
    }
}