package com.link.riderservice.feature.connection.mode

/**
 * WiFi连接模式枚举
 * 支持AP客户端模式和P2P直连模式
 */
enum class WifiConnectionMode {
    /**
     * AP客户端模式 - 连接到仪表端WiFi热点
     * 仪表端作为WiFi热点，手机作为客户端连接
     */
    WIFI_AP_CLIENT,

    /**
     * P2P直连模式
     * 使用WiFi Direct技术直接连接
     */
    WIFI_P2P;

    companion object {
        /**
         * 默认连接模式
         */
        const val DEFAULT_MODE = "WIFI_AP_CLIENT"

        /**
         * 从字符串创建枚举
         */
        fun fromString(mode: String?): WifiConnectionMode {
            return try {
                if (mode.isNullOrEmpty()) {
                    valueOf(DEFAULT_MODE)
                } else {
                    valueOf(mode)
                }
            } catch (e: IllegalArgumentException) {
                valueOf(DEFAULT_MODE)
            }
        }
    }
}