package com.link.riderservice.api.dto

/**
 * 导航路线信息，发送给仪表盘
 * @param distance 距离
 * @param latitude 纬度
 * @param longitude 经度
 * @param notifyType 通知类型
 * @param reason 通知原因
 * @param roadName 路名
 * @param subTitle 副标题
 * @param isSuccess 是否成功
 */
data class NaviRoute(
    val distance: Int,
    val latitude: Double,
    val longitude: Double,
    val notifyType: Int,
    val reason: String,
    val roadName: String,
    val subTitle: String,
    val isSuccess: Boolean,
) : RiderMessage