// Copyright 2014 Google Inc. All Rights Reserved.

#include "util/common.h"
#include "GalReceiver.h"

bool GalReceiver::init(const shared_ptr<IControllerCallbacks> &controllerCallbacks) {
    bool ret = mChannelManager.init(&mMessageRouter);
    ret |= mMessageRouter.init(&mChannelManager);

    // To make sure the channel 0 is alloc to controller,
    // the controller should be the first service to be registered.
    ret |= registerService(&mController);
    int controlChannelId = mChannelManager.allocChannel(0, CHANNEL_PRIORITY_HIGHEST);
    mMessageRouter.setupMapping(CONTROLLER_SERVICE_ID, controlChannelId);
    mController.registerCallbacks(controllerCallbacks);
    mController.onChannelOpened(controlChannelId, 0);
    return ret;
}

void GalReceiver::start() {
    mController.start();
}

void GalReceiver::prepareShutdown() {
    mChannelManager.prepareShutdown();
}

void GalReceiver::shutdown() {
    mMessageRouter.shutdown();
    mChannelManager.shutdown();
    mController.shutdown();
}

int GalReceiver::queueIncoming(void *raw, size_t len) {
    shared_ptr<IoBuffer> buf(new IoBuffer(len));
    memcpy(buf->raw(), raw, len);
    return mChannelManager.queueIncoming(buf);
}

shared_ptr<IoBuffer> GalReceiver::allocateBuffer(size_t size) {
    shared_ptr<IoBuffer> buf(new IoBuffer(size));
    return buf;
}

int GalReceiver::queueIncoming(const shared_ptr<IoBuffer> &buf) {
    return mChannelManager.queueIncoming(buf);
}

int GalReceiver::getAdditionalBytesToRead(unsigned char buf[FRAME_HEADER_MIN_LENGTH]) {
    return ChannelManager::getAdditionalBytesToRead(buf);
}

bool GalReceiver::getEncodedFrame(IoBuffer *buf) {
    return mChannelManager.getEncodedFrame(buf);
}

bool GalReceiver::registerService(ProtocolEndpointBase *endpoint) {
    return mMessageRouter.registerService(endpoint);
}





