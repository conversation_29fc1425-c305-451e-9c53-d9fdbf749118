// Copyright 2014 Google Inc. All Rights Reserved.

#include "ControllerCallbacks.h"

void ControllerCallbacks::unrecoverableErrorCallback(MessageStatus err) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mUnrecoverableErrorCallbackId, (jint) err);
}

void ControllerCallbacks::authCompleteCallback() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mAuthCompelteCallbackId);
}

void ControllerCallbacks::pingRequestCallback(int64_t timestamp, bool bugReport) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mPingRequestCallbackId, timestamp, bugReport);
}

void ControllerCallbacks::pingResponseCallback(int64_t timestamp) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mPingResponseCallbackId, timestamp);
}

void ControllerCallbacks::byeByeRequestCallback(ByeByeReason reason) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mByeByeRequestCallbackId, reason);
}

void ControllerCallbacks::byeByeResponseCallback() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mByeByeResponseCallbackId);
}

void ControllerCallbacks::exitRequestCallback() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mExitRequestCallbackId);
}

/*void ControllerCallbacks::navFocusNotificationCallback(NavFocusType focusType) {
    JNIEnv* env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mNavFocusNotifCallbackId, focusType);
}

void ControllerCallbacks::audioFocusNotificationCallback(AudioFocusStateType request,bool unsolicited) {
    JNIEnv* env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mAudioFoucNotifCallbackId, request);
}*/

void ControllerCallbacks::forceLandscapeRequestCallback(bool force) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mForceLandscapeCallbackId, force);
}

void ControllerCallbacks::screenOrientationInquire() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mScreenOrientationInquireCallbackId);
}

void ControllerCallbacks::screenResolutionInquire() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mScreenResolutionInquireCallbackId);
}

void ControllerCallbacks::runningStateInquire() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    //env->CallVoidMethod(mJthis, mRunningStateInquireCallbackId);
}

void ControllerCallbacks::versionResponseCallback(uint16_t major, uint16_t minor) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mVersionResponseCallbackId, major, minor);
}

void ControllerCallbacks::serviceDiscoveryResponseCallback(string &id, string &make, string &model,
                                                           string &year, string &huIc,
                                                           string &huMake,
                                                           string &huModel, string &huSwBuild,
                                                           string &huSwVersion,
                                                           string &huSeries, string &huMuVersion,
                                                           int32_t checkSum) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    jstring jId = nullptr;
    jstring jMake = nullptr;
    jstring jModel = nullptr;
    jstring jYear = nullptr;
    jstring jHuIc = nullptr;
    jstring jHuMake = nullptr;
    jstring jHuModel = nullptr;
    jstring jHuSwBuild = nullptr;
    jstring jHuSwVersion = nullptr;
    jstring jHuSeries = nullptr;
    jstring jHuMuVersion = nullptr;
    if (!id.empty()) {
        jId = env->NewStringUTF(id.c_str());
    }
    if (!make.empty()) {
        jMake = env->NewStringUTF(make.c_str());
    }
    if (!model.empty()) {
        jModel = env->NewStringUTF(model.c_str());
    }
    if (!year.empty()) {
        jYear = env->NewStringUTF(year.c_str());
    }
    if (!huIc.empty()) {
        jHuIc = env->NewStringUTF(huIc.c_str());
    }
    if (!huMake.empty()) {
        jHuMake = env->NewStringUTF(huMake.c_str());
    }


    if (!huModel.empty()) {
        jHuModel = env->NewStringUTF(huModel.c_str());
    }
    if (!huSwBuild.empty()) {
        jHuSwBuild = env->NewStringUTF(huSwBuild.c_str());
    }
    if (!huSwVersion.empty()) {
        jHuSwVersion = env->NewStringUTF(huSwVersion.c_str());
    }

    if (!huSeries.empty()) {
        jHuSeries = env->NewStringUTF(huSeries.c_str());
    }
    if (!huMuVersion.empty()) {
        jHuMuVersion = env->NewStringUTF(huMuVersion.c_str());
    }
    env->CallVoidMethod(mJthis, mserviceDiscoveryReponseCallbackId, jId, jMake, jModel, jYear,
                        jHuIc, jHuMake, jHuModel,
                        jHuSwBuild, jHuSwVersion, jHuSeries, jHuMuVersion, checkSum);
}

void ControllerCallbacks::autoRotationRequest(bool autoed) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mAutoRotationRequestId, autoed);
}

void ControllerCallbacks::timeDateInquire() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mTimeDateInquireCallbackId);
}
