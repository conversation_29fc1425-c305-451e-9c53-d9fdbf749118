package com.link.riderservice.feature.connection.transport

import java.io.IOException

interface Transport {
    /**
     * 停止传输
     */
    fun stopTransport()

    /**
     * 读数据
     * @param buffer   buffer
     * @param offset 偏移量
     * @param length 期望读取的数据长度
     * @return 读取到的数据长度
     * @throws IOException
     */
    @Throws(IOException::class)
    fun read(buffer: ByteArray, offset: Int, length: Int): Int

    /**
     * 写数据
     * @param buffer   buffer
     * @param offset 偏移量
     * @param length 数据长度
     * @throws IOException
     */
    @Throws(IOException::class)
    fun write(buffer: ByteArray, offset: Int, length: Int)

    /**
     * Socket 是否连接
     */
    fun isConnected(): Bo<PERSON>an
}