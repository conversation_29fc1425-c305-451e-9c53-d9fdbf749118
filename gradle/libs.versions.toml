[versions]
agp = "8.6.1"
kotlin = "1.8.0"
dokka = "1.7.20"
kotlinx-coroutines-android = "1.7.3"
protobuf = "0.9.3"

androidx-core-ktx = "1.9.0"
androidx-startup = "1.1.1"

protoc = "2.6.1"
protobuf-java = "2.6.1"

junit = "4.13.2"
androidx-test-ext = "1.2.1"
espresso-core = "3.6.1"

compileSdk = "35"
minSdk = "24"

[libraries]
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "androidx-core-ktx" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinx-coroutines-android" }
protoc = { module = "com.google.protobuf:protoc", version.ref = "protoc" }
protobuf-java = { module = "com.google.protobuf:protobuf-java", version.ref = "protobuf-java" }
androidx-startup = {module = "androidx.startup:startup-runtime", version.ref = "androidx-startup"}

junit = { module = "junit:junit", version.ref = "junit" }
androidx-test-ext = { module = "androidx.test.ext:junit", version.ref = "androidx-test-ext" }
espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "espresso-core" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
jetbrains-dokka = { id = "org.jetbrains.dokka", version.ref = "dokka" }
google-protobuf = { id = "com.google.protobuf", version.ref = "protobuf" }
