package com.link.riderservice.api.dto

/**
 * 车道信息，发送给仪表盘
 * @property backgroundLane 后车道
 * @property frontLane 前车道
 * @property laneCount 车道数量
 */
data class LaneInfo(
    val backgroundLane: IntArray,
    val frontLane: IntArray,
    val laneCount: Int,
) : RiderMessage {

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as LaneInfo

        if (!backgroundLane.contentEquals(other.backgroundLane)) return false
        if (!frontLane.contentEquals(other.frontLane)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = backgroundLane.contentHashCode()
        result = 31 * result + frontLane.contentHashCode()
        return result
    }
}
