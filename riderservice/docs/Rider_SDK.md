# Rider SDK

## RiderService

### 1. 生命周期管理 API

整个SDK的生命周期管理，包括初始化、销毁和状态管理

### 2. 连接管理 API

管理蓝牙和WiFi连接

#### 2.1 蓝牙连接

蓝牙扫描、停止扫描、连接、断开连接、获取连接状态、获取当前连接设备、自动连接等

#### 2.2 WiFi连接

设置WIFI连接的模式，是否在初始化SDK的时候也采用Builder模式设置

### 3. 消息发送 API

导航信息的发送，其他有需要的消息发送，可以根据需求添加

### 4. 导航控制 API

主要是切换导航的模式，如投屏、镜像等

### 5. 投屏控制 API

android 主要是需要提供Display给上层，让上层去显示内容、还有就是镜像的时候，需要上层支持

ios 等待亚军补充

### 6. 配置管理 API

获取SDK相关配置的方法，已经获取仪表相关信息的方法

### 7. 回调管理 API

各种callback，包括连接、导航、错误消息等

### 8. 日志系统 API

控制SDK的日志显示和存储，方便debug

## 地图 SDK

需不需要提供，怎么提供？

## 现在 Android 的 RiderService SDK 详细API分类表

| 功能模块       | API方法                          | 访问级别     | 备注             |
|------------|--------------------------------|----------|----------------|
| **生命周期**   | `init()`                       | Internal | 内部调用，应用层通过单例获取 |
|            | `destroy()`                    | Public   | 释放所有资源         |
|            | `getApplication()`             | Public   | 获取应用上下文        |
| **蓝牙连接**   | `startBleScan()`               | Public   | 支持自动连接参数       |
|            | `stopBleScan()`                | Public   | 停止扫描           |
|            | `connect()`                    | Public   | 手动连接指定设备       |
|            | `disconnect()`                 | Public   | 支持手动/自动断开标识    |
|            | `getConnectStatus()`           | Public   | 返回详细连接状态       |
|            | `getCurrentConnectDevice()`    | Public   | 可能返回null       |
| **WiFi连接** | `setWifiConnectionMode()`      | Public   | 支持AP/P2P模式     |
|            | `getWifiConnectionMode()`      | Public   | 获取当前连接模式       |
| **消息发送**   | `sendMessageToRiderService()`  | Public   | 统一消息发送接口       |
| **导航控制**   | `setNaviMode()`                | Public   | 设置本地导航模式       |
|            | `sendNaviModeChange()`         | Public   | 发送模式切换请求       |
|            | `sendNaviModeStart()`          | Public   | 发送导航开始         |
|            | `sendNaviModeStop()`           | Public   | 发送导航停止         |
|            | `sendNaviModeChangeResponse()` | Public   | 响应模式切换         |
|            | `needChangeDisplay()`          | Public   | 检查显示切换需求       |
| **投屏控制**   | `setMediaProjection()`         | Public   | 设置媒体投射权限       |
|            | `requestLockScreenDisplay()`   | Public   | 请求锁屏显示         |
|            | `initLockScreenProjection()`   | Public   | 初始化锁屏投射        |
|            | `startScreenNavi()`            | Internal | 内部调用           |
|            | `stopScreenNavi()`             | Internal | 内部调用           |
| **配置管理**   | `getConfigPreferences()`       | Public   | 获取配置管理器        |
|            | `deleteConfig()`               | Public   | 清除配置           |
|            | `getProductKey()`              | Public   | 获取产品密钥         |
|            | `getUuid()`                    | Public   | 获取设备UUID       |
|            | `getAutolinkVersion()`         | Public   | 获取版本信息         |
| **回调管理**   | `addCallback()`                | Public   | 添加事件监听器        |
|            | `removeCallback()`             | Public   | 移除事件监听器        |

| 回调方法                      | 参数                   | 描述       |
|---------------------------|----------------------|----------|
| `onScanResult()`          | `List<BleDevice>`    | 蓝牙扫描结果   |
| `onScanning()`            | -                    | 开始扫描     |
| `onScanFinish()`          | -                    | 扫描完成     |
| `onConnectStatusChange()` | `Connection`         | 连接状态变化   |
| `onConfigChange()`        | `RiderServiceConfig` | 仪表配置变化   |
| `onNaviModeChange()`      | `NaviMode`           | 导航模式变化   |
| `onDisplayInitialized()`  | `Display`            | 显示屏初始化   |
| `onDisplayReleased()`     | `Display`            | 显示屏释放    |
| `onVideoChannelReady()`   | -                    | 视频通道准备就绪 |
| `onRequestWeatherInfo()`  | -                    | 请求天气信息   |
| `onClusterReady()`        | -                    | 仪表准备就绪   |
| `onDialogShow()`          | `String`, `String`   | 显示对话框    |

## 现在 Android 的 RiderService SDK API 问题

| 问题类型        | 具体表现                                       | 影响程度 |
|-------------|--------------------------------------------|------|
| **命名不一致**   | `startBleScan()` vs `sendNaviModeChange()` | 高    |
| **参数风格混乱**  | 有些用对象，有些用基本类型                              | 中    |
| **返回值不统一**  | 有些返回值，有些void，有些抛异常                         | 高    |
| **异步处理不一致** | 有些同步，有些异步，有些回调                             | 高    |
| **职责混乱**    | 单一方法处理多种消息类型                               | 高    |