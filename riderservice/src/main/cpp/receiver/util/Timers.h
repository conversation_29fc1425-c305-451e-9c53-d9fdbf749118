//
// Created by w.feng on 2020/5/27.
//

#ifndef AUTOLINK_TIMERS_H
#define AUTOLINK_TIMERS_H

#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif


typedef int64_t nsecs_t;

enum {
    SYSTEM_TIME_REALTIME = 0,  // system-wide realtime clock
    SYSTEM_TIME_MONOTONIC = 1, // monotonic time since unspecified starting point
    SYSTEM_TIME_PROCESS = 2,   // high-resolution per-process clock
    SYSTEM_TIME_THREAD = 3,    // high-resolution per-thread clock
    SYSTEM_TIME_BOOTTIME = 4   // same as SYSTEM_TIME_MONOTONIC, but including CPU suspend time
};

// return the system-time according to the specified clock
#ifdef __cplusplus
nsecs_t systemTime(int clock = SYSTEM_TIME_MONOTONIC);
#else
nsecs_t systemTime(int clock);
#endif // def __cplusplus


#ifdef __cplusplus
} // extern "C"
#endif
#endif //AUTOLINK_TIMERS_H
