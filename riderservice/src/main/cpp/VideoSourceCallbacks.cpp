// Copyright 2014 Google Inc. All Rights Reserved.

#include "VideoSourceCallbacks.h"

int VideoSourceCallbacks::onChannelOpened() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    return env->CallIntMethod(mJthis, mChannelOpendCallbackId);
}

int VideoSourceCallbacks::configCallback(
        int32_t status,
        uint32_t maxUnack,
        uint32_t *prefer,
        uint32_t size) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    jintArray array = env->NewIntArray(size);
    env->SetIntArrayRegion(array, 0, size, (jint *) prefer);
    env->CallIntMethod(mJthis, mConfigCallbackId, status, maxUnack, array, size);
    env->DeleteLocalRef(array);
    return 0;
}

int VideoSourceCallbacks::ackCallback(int32_t sessionId, uint32_t numFrames) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    return env->CallIntMethod(mJthis, mAckallbackId, sessionId, numFrames);
}

int VideoSourceCallbacks::videoFocusNotifCallback(int32_t focus, bool unsolicited) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    return env->CallIntMethod(mJthis, mVideoFoucsNotifCallbackId, focus, unsolicited);
}

int VideoSourceCallbacks::displayChangeCallback(
        int32_t width,
        int32_t height,
        bool isLandscape,
        int32_t density) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    return env->CallIntMethod(
            mJthis,
            mDisplayChangeCallbackId,
            width,
            height,
            isLandscape,
            density);
}

bool VideoSourceCallbacks::discoverVideoConfigCallback(
        int32_t codec,
        int32_t fps,
        int32_t w,
        int32_t h) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    return env->CallBooleanMethod(
            mJthis,
            mDiscoverVideoConfigCallbackId,
            codec,
            fps,
            w,
            h);
}

int VideoSourceCallbacks::startResponseCallback(bool isOk) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    return env->CallIntMethod(
            mJthis,
            mStartResponseCallbackCallbackId,
            isOk);
}

int VideoSourceCallbacks::stopResponseCallback() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    return env->CallIntMethod(
            mJthis,
            mStopResponseCallbackCallbackId);
}
