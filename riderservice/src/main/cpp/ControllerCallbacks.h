// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef ANDROID_AUTO_PROJECTION_PROTOCOL_CONTROLLER_CALLBACKS_H
#define ANDROID_AUTO_PROJECTION_PROTOCOL_CONTROLLER_CALLBACKS_H

#include "NativeCallbackWrapper.h"
#include "util/common.h"
#include "IControllerCallbacks.h"


class ControllerCallbacks : public NativeCallbackWrapper, public IControllerCallbacks {
public:
    ControllerCallbacks(JNIEnv *env, jobject jthis) : NativeCallbackWrapper(env, jthis) {
        LOGE("ControllerCallbacks init");
        mUnrecoverableErrorCallbackId = env->GetMethodID(mThisClass,
                                                         "unrecoverableErrorCallback",
                                                         "(I)V");
        mAuthCompelteCallbackId = env->GetMethodID(mThisClass,
                                                   "authCompleteCallback",
                                                   "()V");
        mPingRequestCallbackId = env->GetMethodID(mThisClass,
                                                  "pingRequestCallback",
                                                  "(JZ)V");
        mPingResponseCallbackId = env->GetMethodID(mThisClass,
                                                   "pingResponseCallback",
                                                   "(J)V");
        mNavFocusNotifCallbackId = env->GetMethodID(mThisClass,
                                                    "navigationFocusCallback",
                                                    "(I)V");
        mByeByeRequestCallbackId = env->GetMethodID(mThisClass,
                                                    "byeByeRequestCallback",
                                                    "(I)V");
        mByeByeResponseCallbackId = env->GetMethodID(mThisClass,
                                                     "byeByeResponseCallback",
                                                     "()V");
        mExitRequestCallbackId = env->GetMethodID(mThisClass,
                                                  "exitRequestCallback",
                                                  "()V");
        mAudioFoucNotifCallbackId = env->GetMethodID(mThisClass,
                                                     "audioFocusNotificationCallback",
                                                     "(IZ)V");
        mForceLandscapeCallbackId = env->GetMethodID(mThisClass,
                                                     "forceLandscapeRequestCallback",
                                                     "(Z)V");
        mScreenOrientationInquireCallbackId = env->GetMethodID(mThisClass,
                                                               "screenOrientationInquire",
                                                               "()V");
        mVersionResponseCallbackId = env->GetMethodID(mThisClass,
                                                      "versionResponseCallback",
                                                      "(SS)V");
        mserviceDiscoveryReponseCallbackId = env->GetMethodID(mThisClass,
                                                              "serviceDiscoveryResponseCallback",
                                                              "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V");
        mAutoRotationRequestId = env->GetMethodID(mThisClass,
                                                  "autoRotationRequest",
                                                  "(Z)V");


        mScreenResolutionInquireCallbackId = env->GetMethodID(mThisClass,
                                                              "screenResolutionInquire",
                                                              "()V");
        mTimeDateInquireCallbackId = env->GetMethodID(mThisClass,
                                                      "timeDateInquire",
                                                      "()V");

    }

    void unrecoverableErrorCallback(MessageStatus err) override;

    void authCompleteCallback() override;

    void pingRequestCallback(int64_t timestamp, bool bugReport) override;

    void pingResponseCallback(int64_t timestamp) override;

    void byeByeRequestCallback(ByeByeReason reason) override;

    void byeByeResponseCallback() override;

    void exitRequestCallback() override;

    //void navFocusNotificationCallback(NavFocusType type);
    //void audioFocusNotificationCallback(AudioFocusStateType status, bool unsolicited);
    void forceLandscapeRequestCallback(bool force) override;

    void screenOrientationInquire() override;

    void screenResolutionInquire() override;

    void runningStateInquire() override;

    void timeDateInquire() override;

    void versionResponseCallback(uint16_t major, uint16_t minor) override;

    void serviceDiscoveryResponseCallback(string &id, string &make, string &model,
                                          string &year, string &huIc, string &huMake,
                                          string &huModel, string &huSwBuild, string &huSwVersion,
                                          string &huSeries, string &huMuVersion, int32_t checkSum) override;

    void autoRotationRequest(bool autoed) override;

private:
    jmethodID mUnrecoverableErrorCallbackId;
    jmethodID mAuthCompelteCallbackId;
    jmethodID mPingRequestCallbackId;
    jmethodID mPingResponseCallbackId;
    jmethodID mNavFocusNotifCallbackId;
    jmethodID mByeByeRequestCallbackId;
    jmethodID mByeByeResponseCallbackId;
    jmethodID mExitRequestCallbackId;
    jmethodID mAudioFoucNotifCallbackId;
    jmethodID mForceLandscapeCallbackId;
    jmethodID mScreenOrientationInquireCallbackId;
    jmethodID mVersionResponseCallbackId;
    jmethodID mserviceDiscoveryReponseCallbackId;
    jmethodID mAutoRotationRequestId;
    jmethodID mScreenResolutionInquireCallbackId;
    jmethodID mTimeDateInquireCallbackId;
};

#endif // ANDROID_AUTO_PROJECTION_PROTOCOL_CONTROLLER_CALLBACKS_H
