package com.link.riderservice.feature.display.presentation

import android.app.Presentation
import android.content.Context
import android.os.Bundle
import android.view.Display
import android.view.WindowManager
import com.link.riderservice.R

/**
 * <AUTHOR>
 * @date 2023/8/8
 */
class LockScreenPresentation @JvmOverloads constructor(
    outerContext: Context,
    display: Display,
    theme: Int = 0
) : Presentation(outerContext, display, theme) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.lock_screen_main)
    }

    init {
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        window?.addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
        window?.addFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
        window?.addFlags(WindowManager.LayoutParams.FLAG_LOCAL_FOCUS_MODE)
        window?.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        window?.setType(WindowManager.LayoutParams.TYPE_PRIVATE_PRESENTATION)
        window?.clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
    }
}