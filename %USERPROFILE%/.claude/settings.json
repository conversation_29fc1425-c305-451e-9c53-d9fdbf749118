{"statusLine": {"type": "command", "command": "powershell.exe -NoProfile -Command \"$input = $input | ConvertFrom-Json; $cwd = Split-Path -Leaf $input.workspace.current_dir; $branch = try { git rev-parse --abbrev-ref HEAD 2>$null } catch { $null }; $model = $input.model.display_name; $time = Get-Date -Format 'HH:mm:ss'; if ($branch) { Write-Host \\\"$model | $cwd ($branch) | $time\\\" } else { Write-Host \\\"$model | $cwd | $time\\\" }\""}}