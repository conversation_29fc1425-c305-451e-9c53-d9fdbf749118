package com.link.riderservice.core.utils.ble

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.content.Context
import android.content.pm.PackageManager
import android.location.LocationManager
import android.os.Build
import android.os.ParcelUuid
import androidx.annotation.ChecksSdkIntAtLeast
import androidx.core.content.ContextCompat
import androidx.core.location.LocationManagerCompat
import com.link.riderservice.libs.ble.scanner.ScanResult

internal object BleUtils {
    private val EDDYSTONE_UUID = ParcelUuid.fromString("0000FEAA-0000-1000-8000-00805f9b34fb")
    private const val COMPANY_ID_MICROSOFT = 0x0006
    private const val COMPANY_ID_APPLE = 0x004C
    private const val COMPANY_ID_NORDIC_SEMI = 0x0059

    fun isBleEnabled(): Boolean {
        val adapter = BluetoothAdapter.getDefaultAdapter()
        return adapter != null && adapter.isEnabled
    }

    @SuppressWarnings("MissingPermission")
    fun enableBluetooth(): Boolean {
        val adapter = BluetoothAdapter.getDefaultAdapter()
        if (adapter != null && !adapter.isEnabled) {
            return adapter.enable()
        }
        return true
    }

    fun isLocationEnabled(context: Context): Boolean {
        val lm = context.getSystemService(LocationManager::class.java)
        return LocationManagerCompat.isLocationEnabled(lm)
    }

    fun isLocationPermissionGranted(context: Context): Boolean {
        return (ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION)
                == PackageManager.PERMISSION_GRANTED)
    }

    fun isBluetoothScanPermissionGranted(context: Context): Boolean {
        return if (!isSorAbove()) true else ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.BLUETOOTH_SCAN
        ) == PackageManager.PERMISSION_GRANTED
    }

    fun isBluetoothConnectPermissionGranted(context: Context): Boolean {
        return if (!isSorAbove()) true else ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.BLUETOOTH_CONNECT
        ) == PackageManager.PERMISSION_GRANTED
    }

    @ChecksSdkIntAtLeast(api = Build.VERSION_CODES.S)
    fun isSorAbove(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
    }

    fun isBeacon(result: ScanResult): Boolean {
        if (result.scanRecord != null) {
            val record = result.scanRecord
            val appleData =
                record!!.getManufacturerSpecificData(COMPANY_ID_APPLE)
            if (appleData != null) {
                // iBeacons
                if (appleData.size == 23 && appleData[0] == (0x02).toByte() && appleData[1] == (0x15).toByte()) return true
            }
            val nordicData =
                record.getManufacturerSpecificData(COMPANY_ID_NORDIC_SEMI)
            if (nordicData != null) {
                // Nordic Beacons
                if (nordicData.size == 23 && nordicData[0] == (0x02).toByte() && nordicData[1] == (0x15).toByte()) return true
            }
            val microsoftData =
                record.getManufacturerSpecificData(COMPANY_ID_MICROSOFT)
            if (microsoftData != null) {
                // Microsoft Advertising Beacon
                if (microsoftData[0] == (0x01).toByte()) // Scenario Type = Advertising Beacon
                    return true
            }

            // Eddystone
            val eddystoneData =
                record.getServiceData(EDDYSTONE_UUID)
            if (eddystoneData != null) return true
        }
        return false
    }

    fun isAirDrop(result: ScanResult): Boolean {
        if (result.scanRecord != null) {
            val record = result.scanRecord

            // iPhones and iMacs advertise with AirDrop packets
            val appleData =
                record!!.getManufacturerSpecificData(COMPANY_ID_APPLE)
            return appleData != null && appleData.size > 1 && appleData[0] == (0x10).toByte()
        }
        return false
    }

    fun isTarget(result: ScanResult): Boolean {
        if (result.scanRecord != null) {
            val address = result.device.address
            val record = result.scanRecord
            if (address.replace(":", "") == record?.deviceName) {
                return true
            }
        }
        return false
    }


}