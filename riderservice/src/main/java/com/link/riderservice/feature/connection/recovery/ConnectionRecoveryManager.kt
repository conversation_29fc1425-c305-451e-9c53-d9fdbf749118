package com.link.riderservice.feature.connection.recovery

import android.content.Context
import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.logging.logE
import com.link.riderservice.core.utils.logging.logW
import com.link.riderservice.feature.connection.coordinator.ConnectionManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 * 连接恢复管理器
 * 处理WiFi连接异常和自动恢复机制
 */
internal class ConnectionRecoveryManager {

    private val isRecovering = AtomicBoolean(false)
    private val recoveryAttempts = AtomicInteger(0)
    private var recoveryJob: Job? = null

    companion object {
        private const val TAG = "ConnectionRecoveryManager"
        private const val MAX_RECOVERY_ATTEMPTS = 3
        private const val RECOVERY_DELAY_MS = 2000L
        private const val RECOVERY_TIMEOUT_MS = 30000L
    }

    /**
     * 错误类型枚举
     */
    enum class ErrorType {
        PERMISSION_ERROR,           // 权限错误
        WIFI_DISABLED,             // WiFi未启用
        NETWORK_UNAVAILABLE,       // 网络不可用
        CONNECTION_TIMEOUT,        // 连接超时
        AUTHENTICATION_FAILED,     // 认证失败
        IP_ACQUISITION_FAILED,     // IP获取失败
        TCP_CONNECTION_FAILED,     // TCP连接失败
        UNKNOWN_ERROR             // 未知错误
    }

    /**
     * 恢复策略枚举
     */
    enum class RecoveryStrategy {
        RETRY_CURRENT_MODE,        // 重试当前模式
        RESET_AND_RETRY,          // 重置后重试
        SHOW_USER_GUIDANCE,       // 显示用户引导
        DISABLE_FEATURE           // 禁用功能
    }

    /**
     * 错误处理监听器
     */
    interface ErrorHandlerListener {
        fun onRecoveryStarted(errorType: ErrorType)
        fun onRecoveryProgress(attempt: Int, maxAttempts: Int)
        fun onRecoverySuccess(strategy: RecoveryStrategy)
        fun onRecoveryFailed(errorType: ErrorType, finalStrategy: RecoveryStrategy)
        fun onUserGuidanceRequired(errorType: ErrorType, guidance: String)
    }

    private var listener: ErrorHandlerListener? = null

    /**
     * 设置错误处理监听器
     */
    fun setErrorHandlerListener(listener: ErrorHandlerListener) {
        this.listener = listener
    }

    /**
     * 处理连接错误
     */
    fun handleConnectionError(
        errorType: ErrorType,
        context: Context,
        connectionManager: ConnectionManager
    ) {
        logE(TAG, "Handling connection error: $errorType")

        if (isRecovering.get()) {
            logW(TAG, "Recovery already in progress, ignoring new error")
            return
        }

        val strategy = determineRecoveryStrategy(errorType)
        logD(TAG, "Recovery strategy determined: $strategy")

        when (strategy) {
            RecoveryStrategy.RETRY_CURRENT_MODE -> {
                startAutomaticRecovery(errorType, context, connectionManager)
            }

            RecoveryStrategy.RESET_AND_RETRY -> {
                resetAndRetry(errorType, context, connectionManager)
            }

            RecoveryStrategy.SHOW_USER_GUIDANCE -> {
                showUserGuidance(errorType)
            }

            RecoveryStrategy.DISABLE_FEATURE -> {
                disableFeature(errorType)
            }
        }
    }

    /**
     * 确定恢复策略
     */
    private fun determineRecoveryStrategy(errorType: ErrorType): RecoveryStrategy {
        return when (errorType) {
            ErrorType.PERMISSION_ERROR -> RecoveryStrategy.SHOW_USER_GUIDANCE
            ErrorType.WIFI_DISABLED -> RecoveryStrategy.SHOW_USER_GUIDANCE
            ErrorType.NETWORK_UNAVAILABLE -> RecoveryStrategy.RETRY_CURRENT_MODE
            ErrorType.CONNECTION_TIMEOUT -> RecoveryStrategy.RESET_AND_RETRY
            ErrorType.AUTHENTICATION_FAILED -> RecoveryStrategy.SHOW_USER_GUIDANCE
            ErrorType.IP_ACQUISITION_FAILED -> RecoveryStrategy.RESET_AND_RETRY
            ErrorType.TCP_CONNECTION_FAILED -> RecoveryStrategy.RETRY_CURRENT_MODE
            ErrorType.UNKNOWN_ERROR -> RecoveryStrategy.RESET_AND_RETRY
        }
    }

    /**
     * 开始自动恢复
     */
    private fun startAutomaticRecovery(
        errorType: ErrorType,
        context: Context,
        connectionManager: ConnectionManager
    ) {
        if (isRecovering.compareAndSet(false, true)) {
            listener?.onRecoveryStarted(errorType)

            recoveryJob = CoroutineScope(Dispatchers.IO).launch {
                try {
                    performRecovery(errorType, context, connectionManager)
                } catch (e: Exception) {
                    logE(TAG, "Recovery failed with exception", e)
                    listener?.onRecoveryFailed(errorType, RecoveryStrategy.RETRY_CURRENT_MODE)
                } finally {
                    isRecovering.set(false)
                    recoveryAttempts.set(0)
                }
            }
        }
    }

    /**
     * 执行恢复操作
     */
    private suspend fun performRecovery(
        errorType: ErrorType,
        context: Context,
        connectionManager: ConnectionManager
    ) {
        repeat(MAX_RECOVERY_ATTEMPTS) { attempt ->
            if (!isRecovering.get()) return

            recoveryAttempts.set(attempt + 1)
            listener?.onRecoveryProgress(attempt + 1, MAX_RECOVERY_ATTEMPTS)

            logD(
                TAG,
                "Recovery attempt ${attempt + 1}/$MAX_RECOVERY_ATTEMPTS for error: $errorType"
            )

            delay(RECOVERY_DELAY_MS)

            when (errorType) {
                ErrorType.NETWORK_UNAVAILABLE -> {
                    if (attemptNetworkRecovery(context, connectionManager)) {
                        listener?.onRecoverySuccess(RecoveryStrategy.RETRY_CURRENT_MODE)
                        return
                    }
                }

                ErrorType.TCP_CONNECTION_FAILED -> {
                    if (attemptTcpRecovery(context, connectionManager)) {
                        listener?.onRecoverySuccess(RecoveryStrategy.RETRY_CURRENT_MODE)
                        return
                    }
                }

                else -> {
                    if (attemptGenericRecovery(context, connectionManager)) {
                        listener?.onRecoverySuccess(RecoveryStrategy.RETRY_CURRENT_MODE)
                        return
                    }
                }
            }
        }

        logE(TAG, "All recovery attempts failed for error: $errorType")
        listener?.onRecoveryFailed(errorType, RecoveryStrategy.RETRY_CURRENT_MODE)
    }

    /**
     * 尝试网络恢复
     */
    private suspend fun attemptNetworkRecovery(
        context: Context,
        connectionManager: ConnectionManager
    ): Boolean {
        return try {
            connectionManager.resetWifiConnectionState()
            delay(1000)
            connectionManager.requestWifiInfo()
            true
        } catch (e: Exception) {
            logE(TAG, "Network recovery failed", e)
            false
        }
    }

    /**
     * 尝试TCP恢复
     */
    private suspend fun attemptTcpRecovery(
        context: Context,
        connectionManager: ConnectionManager
    ): Boolean {
        return try {
            connectionManager.resetWifiConnectionState()
            delay(1000)
            connectionManager.requestWifiInfo()

            withTimeout(RECOVERY_TIMEOUT_MS) {
                delay(3000)
                true
            }
        } catch (e: Exception) {
            logE(TAG, "TCP recovery failed", e)
            false
        }
    }

    /**
     * 尝试通用恢复
     */
    private suspend fun attemptGenericRecovery(
        context: Context,
        connectionManager: ConnectionManager
    ): Boolean {
        return try {
            connectionManager.resetWifiConnectionState()
            delay(2000)
            connectionManager.requestWifiInfo()

            withTimeout(RECOVERY_TIMEOUT_MS) {
                delay(5000)
                true
            }
        } catch (e: Exception) {
            logE(TAG, "Generic recovery failed", e)
            false
        }
    }

    /**
     * 重置并重试
     */
    private fun resetAndRetry(
        errorType: ErrorType,
        context: Context,
        connectionManager: ConnectionManager
    ) {
        connectionManager.resetWifiConnectionState()

        CoroutineScope(Dispatchers.Main).launch {
            delay(RECOVERY_DELAY_MS)
            startAutomaticRecovery(errorType, context, connectionManager)
        }
    }

    /**
     * 显示用户引导
     */
    private fun showUserGuidance(errorType: ErrorType) {
        val guidance = when (errorType) {
            ErrorType.PERMISSION_ERROR -> "请在设置中授予位置权限和WiFi权限"
            ErrorType.WIFI_DISABLED -> "请开启WiFi功能"
            ErrorType.AUTHENTICATION_FAILED -> "WiFi密码可能不正确，请检查网络设置"
            else -> "请检查网络连接并重试"
        }

        listener?.onUserGuidanceRequired(errorType, guidance)
    }

    /**
     * 禁用功能
     */
    private fun disableFeature(errorType: ErrorType) {
        listener?.onRecoveryFailed(errorType, RecoveryStrategy.DISABLE_FEATURE)
    }

    /**
     * 停止恢复过程
     */
    fun stopRecovery() {
        isRecovering.set(false)
        recoveryJob?.cancel()
        recoveryJob = null
        recoveryAttempts.set(0)
    }

    /**
     * 获取当前恢复状态
     */
    fun isRecovering(): Boolean = isRecovering.get()

    /**
     * 获取当前恢复尝试次数
     */
    fun getCurrentRecoveryAttempts(): Int = recoveryAttempts.get()
} 