package com.link.riderservice.data.authorization.domain.entity

import com.link.riderservice.core.utils.serialization.JSON
import com.link.riderservice.core.utils.serialization.JSONParser
import com.link.riderservice.core.utils.serialization.compareTo
import com.link.riderservice.core.utils.serialization.get

data class CheckInfo(
    var status: Int
)

class CheckInfoParser : JSONParser<CheckInfo> {
    override fun parse(json: JSON): CheckInfo {
        val checkInfo = CheckInfo(-1)
        checkInfo::status < json["status"]
        return checkInfo
    }
}

