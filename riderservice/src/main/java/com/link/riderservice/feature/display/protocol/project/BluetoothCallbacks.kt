package com.link.riderservice.feature.display.protocol.project

/**
 * <AUTHOR>
 * @date 2017/9/19
 * @desc Java perspective of the `IBluetoothCallbacks` native interface.
 */
internal interface BluetoothCallbacks :
    NativeCallback {
    /**
     * onChannelOpened
     *
     * @return 0 or -1
     */
    fun onChannelOpened(): Int

    /**
     * discoverBluetoothService
     *
     * @param carAddress    carAddress
     * @param methodsBitmap methodsBitmap
     * @return success or not
     */
    fun discoverBluetoothService(carAddress: String, methodsBitmap: Int): Boolean

    /**
     * onPairingResponse
     *
     * @param status        status
     * @param alreadyPaired alreadyPaired
     */
    fun onPairingResponse(status: Int, alreadyPaired: Boolean)

    /**
     * onAuthenticationData
     *
     * @param authData authData
     */
    fun onAuthenticationData(authData: String?)

    /**
     * onPhoneBluetoothStatusInquire
     */
    fun onPhoneBluetoothStatusInquire()
}