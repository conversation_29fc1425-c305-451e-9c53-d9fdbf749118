package com.link.riderservice.feature.display.manager

import android.content.Context
import android.content.res.Configuration
import android.media.projection.MediaProjection
import android.os.Build
import android.util.DisplayMetrics
import android.view.Display
import android.view.WindowManager
import com.link.riderservice.R
import com.link.riderservice.api.dto.NaviMode
import com.link.riderservice.core.extensions.coroutines.mainScope
import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.feature.connection.transport.Transport
import com.link.riderservice.feature.display.config.AlConfig
import com.link.riderservice.feature.display.config.VideoConfig
import com.link.riderservice.feature.display.protocol.ALIntegration
import com.link.riderservice.feature.display.protocol.project.GalReceiver
import com.link.riderservice.feature.display.protocol.project.Protos
import com.link.riderservice.feature.display.video.MediaProjectService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException

/**
 * <AUTHOR>
 * @date 2017/10/03
 * @desc Autolink Control for everything.
 */
internal class AutolinkControl(private val mContext: Context) {
    private var autolinkIntegration: ALIntegration? = null
    private var currentOrientation = 0
    private var screenWidthPixels = 0
    private var screenHeightPixels = 0
    private val deviceModel: String = Build.MODEL
    private var controlListener: OnControlListener? = null
    private var lastRotationDegrees = 0
    private var mediaProjectionService: MediaProjectService
    private var h264DumpFile: File? = null
    private var h264DumpOutputStream: FileOutputStream? = null
    private var autolinkVersion = "请在连接平台后查看"
    private val rotation: Int
        get() = (mContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager).defaultDisplay
            .rotation

    interface OnControlListener {
        /**
         * 连接丢失
         */
        fun onLoseConnected()

        /**
         * 不能恢复的错误
         * @param errorCode 错误 code
         */
        fun onUnrecoverableError(errorCode: Int)

        /**
         * 连接断开
         */
        fun onDisconnected()

        /**
         * 再见请求
         * @param reason 原因
         */
        fun onByeByeRequest(reason: Int)

        /**
         * 再见请求回复
         */
        fun onByeByeResponse()

        /**
         * 状态改变
         * @param status 状态
         * @see Protos
         */
        fun onStatusChanged(status: Int)

        /**
         * 初始化虚拟显示屏
         * @param display
         */
        fun onDisplayInitialized(display: Display?)

        /**
         * 释放虚拟显示屏
         */
        fun onDisplayReleased(display: Display)

        /**
         * 请求横屏，仪表盘不适用
         * @param isLandscape 横屏或者竖屏
         */
        @Deprecated("仪表盘不适用")
        fun requestLandscape(isLandscape: Boolean)

        /**
         * 请求开启自动旋转
         * @param enableAutoRotation 是否自动旋转
         */
        @Deprecated("仪表盘不适用")
        fun onRequestAutoRotation(enableAutoRotation: Boolean)

        /**
         * 停止传输
         */
        fun stopTransport()

        /**
         * 视频频道已经准备好，可以投屏
         */
        fun onVideoChannelReady()
        fun onRequestMediaProjection()
        fun onMirrorStart()
        fun onMirrorStop()
    }

    /**
     * 注册监听
     * @param listener 监听
     * @see OnControlListener
     */
    fun registerListener(listener: OnControlListener?) {
        controlListener = listener
    }

    /**
     * 发送当前的 focus 给平台，该函数为了控制平台的焦点
     * @param focus [Protos.VIDEO_FOCUS_PROJECTED] or [Protos.VIDEO_FOCUS_NATIVE]
     * @param reason 原因
     * @see Protos
     */
    fun setEncoderState(focus: Int, reason: Int) =
        autolinkIntegration?.sendEncoderState(focus, reason)


    /**
     * 发送手机屏幕信息给平台
     * @param isLandscape 是否横屏
     * @param rotation 旋转角度
     */
    fun sendOrientation(isLandscape: Int, rotation: Int) {
        autolinkIntegration?.sendOrientation(isLandscape, rotation)
        currentOrientation = isLandscape
        lastRotationDegrees = rotation
    }

    /**
     * 发送再见请求给平台
     */
    fun sendByeByeRequest() = autolinkIntegration?.sendByeByeRequest()

    fun setNaviMode(naviMode: NaviMode) {
        mediaProjectionService.setNaviMode(naviMode)
    }

    fun setMediaProjection(mediaProjection: MediaProjection) {
        mediaProjectionService.setMediaProjection(mediaProjection)
    }


    private fun updateAutolinkConfig() {
        val windowManager = mContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val outMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getRealMetrics(outMetrics)
        val orientation = mContext.resources.configuration.orientation
        currentOrientation = orientation
        lastRotationDegrees = rotation
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            screenWidthPixels = outMetrics.heightPixels
            screenHeightPixels = outMetrics.widthPixels
        } else {
            screenWidthPixels = outMetrics.widthPixels
            screenHeightPixels = outMetrics.heightPixels
        }
    }

    private fun sendResolutionNotification(width: Int, height: Int) {
        autolinkIntegration?.sendResolutionNotification(width, height, false)
    }

    /**
     * 与车机建立连接
     * @param transport
     * @see Transport
     */
    fun startConnect(transport: Transport?) {
        startGal(transport)
    }

    /**
     * 释放资源
     */
    @Synchronized
    fun release() {
        logD(TAG, "autolink control release")
        mediaProjectionService.onShutdown()
        autolinkIntegration?.destroy()
        autolinkIntegration = null
        controlListener = null
    }

    private fun createAlConfig(remoteAddress: String? = ""): AlConfig {
        return AlConfig(
            phoneName = deviceModel,
            version = "1.0.11",
            appName = mContext.getString(R.string.config_name),
            screenWidth = screenWidthPixels,
            screenHeight = screenHeightPixels,
            isLandscape = currentOrientation,
            rotation = lastRotationDegrees,
            videoCfg = VideoConfig(isSupportH264 = true, isSupportMpeg4 = false),
            remoteHost = remoteAddress
        )
    }

    private fun startGal(transport: Transport?) {
        autolinkIntegration = ALIntegration()
        autolinkIntegration?.init(
            config = createAlConfig(),
            videoListener = mediaProjectionService,
            appMessageListener = object : GalReceiver.AppMessageListener {
                override fun onAutoRotationRequest(enableAutoRotation: Boolean) {
                    controlListener?.onRequestAutoRotation(enableAutoRotation)
                }

                override fun onDisconnected() {
                    controlListener?.onDisconnected()
                }

                override fun onUnrecoverableError(errorCode: Int) {
                    controlListener?.onUnrecoverableError(errorCode)
                }

                override fun onVersionCallback(majorVersion: Short, minorVersion: Short) {
                    autolinkVersion = "$majorVersion.$minorVersion"
                }

                override fun onCarInfoCallback(
                    carId: String?,
                    carMake: String?,
                    carModel: String?,
                    carYear: String?,
                    headUnitIc: String?,
                    headUnitMake: String?,
                    headUnitModel: String?,
                    headUnitSoftwareBuild: String?,
                    headUnitSoftwareVersion: String?,
                    headUnitSeries: String?,
                    headUnitMultimediaVersion: String?,
                    carInfoChecksum: Int
                ) {

                }

                override fun onScreenOrientationInquire() {
                    sendResolutionNotification(screenWidthPixels, screenHeightPixels)
                }

                override fun onForceLandscapeRequest(isLandscape: Boolean) {
                    controlListener?.requestLandscape(isLandscape)
                }

                override fun onExitRequest() {
                    autolinkIntegration?.exit()
                }

                override fun onRunningState(runningState: Protos.State) {
                    controlListener?.onStatusChanged(runningState.status)
                    if (runningState == Protos.State.PROBE_SUCCESS) {
                        autolinkIntegration?.startAllChannel()
                    }
                }

                override fun onVideoChannelReady() {
                    controlListener?.onVideoChannelReady()
                }

            },
            byeByeHandler = object : GalReceiver.ByeByeHandler {
                override fun onByeByeRequest(reason: Int) {
                    autolinkIntegration?.sendByeByeRequest()
                    controlListener?.onByeByeRequest(reason)
                }

                override fun onByeByeResponse() {
                    controlListener?.onByeByeResponse()
                }

            }
        )
        autolinkIntegration?.start(transport)
    }

    init {
        //initH264File()
        updateAutolinkConfig()
        mediaProjectionService = MediaProjectService(
            mContext,
            onDisplayInitialized = {
                controlListener?.onDisplayInitialized(it)
            },
            onDisplayReleased = {
                controlListener?.onDisplayReleased(it)
            },
            onRequestMediaProjection = {
                controlListener?.onRequestMediaProjection()
            },
            onMirrorStart = {
                controlListener?.onMirrorStart()
            },
            onMirrorStop = {
                controlListener?.onMirrorStop()
            },
            onEncodedFrame = {
                //dumpH264Raw(it.data)
                autolinkIntegration?.sendFrame(it)
            }
        )
    }

    private fun initH264File() {
        h264DumpFile = File(mContext.getExternalFilesDir(null), "test.H264")
        try {
            h264DumpOutputStream = FileOutputStream(h264DumpFile)
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
        }
    }

    private fun dumpH264Raw(data: ByteArray) {
        mainScope.launch(Dispatchers.IO) {
            try {
                h264DumpOutputStream?.write(data)
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    fun requestLockScreenDisplay() {
        autolinkIntegration?.requestLockScreenDisplay()
    }

    fun getAutolinkVersion(): String {
        return autolinkVersion
    }

    companion object {
        private const val TAG = "AutolinkControl"
    }
}