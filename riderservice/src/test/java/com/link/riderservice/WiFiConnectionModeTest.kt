package com.link.riderservice

import com.link.riderservice.connection.WifiConnectionMode
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test

/**
 * WiFi连接模式测试类
 */
class WiFiConnectionModeTest {

    @Test
    fun testWifiConnectionModeEnum() {
        // 测试枚举值
        assertEquals(2, WifiConnectionMode.values().size)
        assertTrue(WifiConnectionMode.values().contains(WifiConnectionMode.WIFI_AP_CLIENT))
        assertTrue(WifiConnectionMode.values().contains(WifiConnectionMode.WIFI_P2P))
    }

    @Test
    fun testFromStringMethod() {
        // 测试从字符串创建枚举
        assertEquals(WifiConnectionMode.WIFI_AP_CLIENT, WifiConnectionMode.fromString("WIFI_AP_CLIENT"))
        assertEquals(WifiConnectionMode.WIFI_P2P, WifiConnectionMode.fromString("WIFI_P2P"))
        
        // 测试默认值
        assertEquals(WifiConnectionMode.WIFI_AP_CLIENT, WifiConnectionMode.fromString(null))
        assertEquals(WifiConnectionMode.WIFI_AP_CLIENT, WifiConnectionMode.fromString(""))
        assertEquals(WifiConnectionMode.WIFI_AP_CLIENT, WifiConnectionMode.fromString("INVALID"))
    }

    @Test
    fun testDefaultMode() {
        assertEquals("WIFI_AP_CLIENT", WifiConnectionMode.DEFAULT_MODE)
    }
} 