package com.link.riderservice.core.utils.ble

data class BeaconItem(var len: Int = 0, var type: Int = 0, var bytes: ByteArray? = null) {
    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as BeaconItem

        return bytes.contentEquals(other.bytes)
    }

    override fun hashCode(): Int {
        return bytes.contentHashCode()
    }
}
