// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef ANDROID_AUTO_PROJECTION_PROTOCOL_JNI_UTIL_H
#define ANDROID_AUTO_PROJECTION_PROTOCOL_JNI_UTIL_H

#include <jni.h>
#include "util/common.h"

class JniUtil {
public:
    static JNIEnv *getEnv(JavaVM *vm) {
        JNIEnv *env = nullptr;
        vm->AttachCurrentThread(&env, nullptr);
        return env;
    }

};

#endif /* ANDROID_AUTO_PROJECTION_PROTOCOL_JNI_UTIL_H */
