// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_CORE_UTIL_IOBUFFER_H
#define AUTOLINK_PROTOCOL_CORE_UTIL_IOBUFFER_H

#include <stddef.h>
#include <stdint.h>
#include <stdlib.h>

/**
 * A class that represents a scoped block of data. Coupled with shared_ptr,
 * this can be used to safely pass around refcounted messages without having
 * to worry too much about leaks.
 */
class IoBuffer {
public:
    /**
     * Create a new IoBuffer.
     * @param size The size of the IoBuffer.
     */
    IoBuffer(size_t size) : mSize(size), mStartOffset(0), mEndOffset(size) {
        mRaw = malloc(size);
    }

    /**
     * Create a new empty IoBuffer.
     */
    IoBuffer() : mRaw(NULL), mSize(0), mStartOffset(0), mEndOffset(0) {}

    /**
     * Releases all memory associated with an IoBuffer.
     */
    ~IoBuffer() {
        free(mRaw);
        mRaw = NULL;
        mSize = 0;
        mStartOffset = 0;
        mEndOffset = 0;
    }

    /**
     * Resize the IoBuffer. This frees any previously managed memory.
     * @param size The new size of this IoBuffer.
     */
    void resize(size_t size) {
        free(mRaw);
        mSize = size;
        mRaw = malloc(size);
        mStartOffset = 0;
        mEndOffset = size;
    }

    /**
     * Set the usable start offset of this IoBuffer. Mostly used in the zero copy path
     * so certain sections can be made inaccessible without having to copy the message
     * around.
     * @param offset The number of bytes to shift the beginning of the pointer that raw() returns.
     */
    void setStartOffset(size_t offset) {
        mStartOffset = offset;
    }

    /**
     * Sets the end of the usable memory. Usually when we decrypt, we do so in place
     * so we want to eliminate the junk at the end of the buffer.
     * @param offset The offset of the end of usable memory from mPtr.
     */
    void setEndOffset(size_t offset) {
        mEndOffset = offset;
    }

    /**
     * Return a pointer to the managed memory.
     * @return A pointer to the managed memory offset by the specified offset.
     */
    void *raw() { return (void *) ((uint8_t *) mRaw + mStartOffset); }

    /**
     * Return the size of the managed memory block.
     * @return The size of the usable memory block.
     */
    size_t size() { return mEndOffset - mStartOffset; }

    /**
     * Get the start offset of this IoBuffer. Use with caution.
     * @return The start offset.
     */
    size_t startOffset() { return mStartOffset; }

    /**
     * Return the end offset of this IoBuffer. Use with caution.
     * @return The end offset.
     */
    size_t endOffset() { return mEndOffset; }

    size_t capacity() const { return mSize; }

    void setInt32Data(int32_t data) { mInt32Data = data; }

    int32_t int32Data() const { return mInt32Data; }

    uint8_t *data() { return (uint8_t *) mRaw + mStartOffset; }

private:
    void *mRaw;
    size_t mSize;
    size_t mStartOffset;
    size_t mEndOffset;
    int32_t mInt32Data;
};

#endif //AUTOLINK_PROTOCOL_CORE_UTIL_IOBUFFER_H
