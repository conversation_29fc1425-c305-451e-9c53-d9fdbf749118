package com.link.riderservice.feature.connection.wifi

import android.Manifest
import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkInfo
import android.net.NetworkRequest
import android.net.wifi.WifiInfo
import android.net.wifi.WifiManager
import android.net.wifi.WifiNetworkSpecifier
import android.os.Build
import android.os.Handler
import android.os.Looper
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import com.link.riderservice.api.RiderService
import com.link.riderservice.core.extensions.coroutines.mainScope
import com.link.riderservice.core.utils.countDownByFlow
import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.logging.logE
import com.link.riderservice.core.utils.logging.logW
import com.link.riderservice.feature.analytics.ConnectionAnalytics
import com.link.riderservice.feature.analytics.ConnectionError
import com.link.riderservice.feature.analytics.ConnectionInfo
import com.link.riderservice.feature.analytics.ConnectionType
import com.link.riderservice.feature.analytics.ErrorType
import com.link.riderservice.feature.analytics.logConnectionPerformance
import com.link.riderservice.feature.analytics.logWifiConnection
import kotlinx.coroutines.Job
import java.net.Inet4Address

/**
 * WiFi客户端管理器
 * 实现连接到仪表端WiFi热点功能，支持Android 6.0-15版本
 */
internal class WiFiClientManager(
    private val listener: WiFiClientManagerListener
) {
    // ====================== [1. Constants & Companion] ======================
    companion object {
        private const val TAG = "WiFiClientManager"
    }

    // ====================== [2. Properties & Initialization] ======================
    private val context = RiderService.instance.getApplication().applicationContext
    private val wifiManager: WifiManager
        get() = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
    private val connectivityManager: ConnectivityManager
        get() = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager


    private var currentState = WifiClientState.WIFI_CLIENT_START
    private var targetSsid: String = ""
    private var targetPassword: String = ""
    private var currentNetwork: Network? = null
    private var reconnectionAttempts = 0
    private val reconnectionHandler = Handler(Looper.getMainLooper())
    private var currentConnectionSessionId: String? = null
    private var connectionStartTime: Long = 0
    private var activeNetworkCallback: ConnectivityManager.NetworkCallback? = null

    // ====================== [3. Broadcast Receivers & Callbacks] ======================
    private val wifiStateReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                    handleWifiStateChanged(intent)
                }

                WifiManager.NETWORK_STATE_CHANGED_ACTION -> {
                    handleNetworkStateChanged(intent)
                }
            }
        }
    }

    init {
        logD(TAG, "WiFiClientManager initialized")
        val filter = IntentFilter().apply {
            addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
            addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION)
        }
        context.registerReceiver(wifiStateReceiver, filter)
    }

    // ====================== [4. Public API Methods] ======================
    fun connectToWifi(ssid: String, password: String) {
        logD(TAG, "Connecting to WiFi: $ssid")
        connectionStartTime = System.currentTimeMillis()
        currentConnectionSessionId = ConnectionAnalytics.recordConnectionStart(
            ConnectionType.WIFI_AP,
            ssid
        )

        logWifiConnection("Attempting to connect", ssid, "AP Mode")

        targetSsid = ssid
        targetPassword = password

        if (!checkPermissions()) {
            logE(TAG, "Missing required permissions")
            updateState(WifiClientState.PERMISSION_DENIED)
            listener.onWifiConnectionFailed(-2)
            return
        }
        logD(TAG, "wifi state: ${wifiManager.wifiState}")
        if (!wifiManager.isWifiEnabled) {
            logE(TAG, "WiFi is not enabled")
            updateState(WifiClientState.WIFI_DISABLED)
            listener.onWifiState(false)
            return
        }


        // 增强的连接状态检查：验证实际网络连接状态
        if (isActuallyConnectedToTarget(ssid)) {
            logD(TAG, "Already connected to target network with valid connection")
            // 通知 ConnectionManager 当前已连接状态
            val ipAddress = getCurrentNetworkIpAddress()
            if (ipAddress.isNotEmpty()) {
                updateState(WifiClientState.CONNECTION_VALIDATED)
                listener.onWifiConnected(ssid, ipAddress)
            }
            return
        }

        updateState(WifiClientState.CHECKING_PERMISSIONS)

        if (!checkPermissions()) {
            updateState(WifiClientState.PERMISSION_DENIED)
            listener.onWifiConnectionFailed(-1)
            return
        }

        updateState(WifiClientState.WIFI_STATE_CHECK)
        updateState(WifiClientState.CONNECTING)
        listener.onWifiConnecting(ssid)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            connectToWifiModern(ssid, password)
        }
    }

    fun disconnect() {
        logD(TAG, "Disconnecting WiFi")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            activeNetworkCallback?.let {
                try {
                    connectivityManager.unregisterNetworkCallback(it)
                } catch (e: Exception) {
                    logW(TAG, "Error during modern disconnect", e)
                }
                activeNetworkCallback = null
            }
        }
    }

    fun checkPermissions(): Boolean {
        val fineLocation = ContextCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        val nearbyWifiDevices = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context, Manifest.permission.NEARBY_WIFI_DEVICES
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }

        return fineLocation && nearbyWifiDevices
    }

    fun isWifiEnabled(): Boolean {
        return wifiManager.isWifiEnabled
    }

    fun getCurrentState(): WifiClientState {
        return currentState
    }

    fun getReconnectionAttempts(): Int {
        return reconnectionAttempts
    }

    fun getCurrentSessionId(): String? {
        return currentConnectionSessionId
    }

    fun resetConnectionState() {
        logD(TAG, "Resetting connection state")
        reconnectionAttempts = 0
        reconnectionHandler.removeCallbacksAndMessages(null)

        // 清理网络回调
        activeNetworkCallback?.let {
            try {
                connectivityManager.unregisterNetworkCallback(it)
            } catch (e: Exception) {
                logW(TAG, "Error unregistering network callback during reset", e)
            }
            activeNetworkCallback = null
        }

        // 重置网络相关状态
        currentNetwork = null
        targetSsid = ""
        targetPassword = ""
        currentConnectionSessionId = null

        // 更全面的状态重置：不仅限于特定状态
        when (currentState) {
            WifiClientState.CONNECTED,
            WifiClientState.CONNECTION_VALIDATED,
            WifiClientState.VALIDATING_CONNECTION,
            WifiClientState.RECONNECTING,
            WifiClientState.CONNECTION_FAILED,
            WifiClientState.CONNECT_FAILED,
            WifiClientState.CONNECTION_INVALID,
            WifiClientState.DISCONNECTED -> {
                updateState(WifiClientState.WIFI_CLIENT_START)
            }

            else -> {
                // 对于其他状态，保持不变
                logD(TAG, "Current state $currentState not reset")
            }
        }
    }

    fun destroy() {
        logD(TAG, "Destroying WiFiClientManager")
        try {
            context.unregisterReceiver(wifiStateReceiver)
        } catch (e: Exception) {
            logE(TAG, "Error unregistering receiver", e)
        }
        reconnectionHandler.removeCallbacksAndMessages(null)
        disconnect()
    }

    // ====================== [5. Connection Implementation] ======================
    @RequiresApi(Build.VERSION_CODES.Q)
    private fun connectToWifiModern(ssid: String, password: String) {
        activeNetworkCallback?.let {
            try {
                connectivityManager.unregisterNetworkCallback(it)
            } catch (e: Exception) {
                logW(TAG, "Error during modern disconnect", e)
            }
            activeNetworkCallback = null
        }
        val wifiNetworkSpecifier = WifiNetworkSpecifier.Builder()
            .setSsid(ssid)
            .setWpa2Passphrase(password)
            .build()

        val networkRequest = NetworkRequest.Builder()
            .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
            .removeCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .setNetworkSpecifier(wifiNetworkSpecifier)
            .build()

        val newCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                logD(TAG, "Network available: $network")
                currentNetwork = network
                val ipAddress = getNetworkIpAddress(network)
                updateState(WifiClientState.CONNECTED)
                updateState(WifiClientState.VALIDATING_CONNECTION)
                if (validateConnection(ipAddress)) {
                    updateState(WifiClientState.CONNECTION_VALIDATED)
                    reconnectionAttempts = 0
                    currentConnectionSessionId?.let { sessionId ->
                        val connectionInfo = ConnectionInfo(
                            ipAddress = ipAddress,
                            ssid = targetSsid,
                            extras = mapOf(
                                "networkId" to network.toString(),
                                "connectionMode" to "AP"
                            )
                        )
                        ConnectionAnalytics.recordConnectionSuccess(sessionId, connectionInfo)
                        val duration = System.currentTimeMillis() - connectionStartTime
                        logConnectionPerformance(ConnectionType.WIFI_AP, duration, targetSsid)
                    }
                    logWifiConnection("Wi-Fi connected, IP: $ipAddress", targetSsid, "AP Mode")
                    listener.onWifiConnected(targetSsid, ipAddress)
                } else {
                    updateState(com.link.riderservice.feature.connection.wifi.WifiClientState.CONNECTION_INVALID)
                    currentConnectionSessionId?.let { sessionId ->
                        val error = ConnectionError(
                            code = -2,
                            message = "WiFi connection validation failed",
                            type = ErrorType.AUTHENTICATION_FAILED
                        )
                        ConnectionAnalytics.recordConnectionFailure(sessionId, error)
                    }
                    logWifiConnection("Wi-Fi connection invalid", targetSsid, "AP Mode")
                    listener.onWifiConnectionFailed(-2)
                }
            }

            override fun onLost(network: Network) {
                super.onLost(network)
                logD(TAG, "Network lost: $network")
                logWifiConnection("Wi-Fi disconnected", targetSsid, "AP Mode")
                if (activeNetworkCallback == this) {
                    activeNetworkCallback = null
                }
                if (currentNetwork == network) {
                    currentNetwork = null
                    updateState(WifiClientState.DISCONNECTED)
                    listener.onWifiDisconnected()
                }
            }

            override fun onUnavailable() {
                super.onUnavailable()
                logD(TAG, "Network unavailable")
                currentConnectionSessionId?.let { sessionId ->
                    val error = ConnectionError(
                        code = -1,
                        message = "WiFi network unavailable",
                        type = ErrorType.NETWORK_UNAVAILABLE
                    )
                    ConnectionAnalytics.recordConnectionFailure(sessionId, error)
                }
                currentConnectionSessionId = null
                logWifiConnection("Wi-Fi connection unavailable", targetSsid, "AP Mode")
                updateState(WifiClientState.CONNECT_FAILED)
                listener.onWifiConnectionFailed(-1)
            }
        }

        try {
            connectivityManager.requestNetwork(networkRequest, newCallback)
            activeNetworkCallback = newCallback
            logD(TAG, "Modern WiFi connection request sent")
        } catch (e: Exception) {
            logE(TAG, "Failed to connect using modern API", e)
            logWifiConnection(
                "Modern API connection failed: ${e.localizedMessage}",
                targetSsid,
                "AP Mode"
            )
            updateState(WifiClientState.CONNECT_FAILED)
            listener.onWifiConnectionFailed(-3)
        }
    }

    // ====================== [6. Network Utilities] ======================
    @SuppressLint("MissingPermission")
    private fun getCurrentSsid(): String {
        return try {
            val wifiInfo = wifiManager.connectionInfo
            wifiInfo?.ssid?.replace("\"", "") ?: ""
        } catch (e: Exception) {
            logE(TAG, "Error getting current SSID", e)
            ""
        }
    }

    private fun getNetworkIpAddress(network: Network): String {
        return try {
            val linkProperties = connectivityManager.getLinkProperties(network)
            linkProperties?.linkAddresses?.find {
                it.address is Inet4Address
            }?.address?.hostAddress ?: ""
        } catch (e: Exception) {
            logE(TAG, "Error getting IP address", e)
            ""
        }
    }

    /**
     * 获取当前网络的IP地址（用于已连接状态检查）
     */
    @SuppressLint("MissingPermission")
    private fun getCurrentNetworkIpAddress(): String {
        return try {
            // 首先尝试从当前网络获取
            currentNetwork?.let { network ->
                val ipAddress = getNetworkIpAddress(network)
                if (ipAddress.isNotEmpty()) {
                    return ipAddress
                }
            }

            // 如果没有当前网络，尝试从活动网络获取
            val activeNetwork = connectivityManager.activeNetwork
            activeNetwork?.let { network ->
                val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
                if (networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true) {
                    return getNetworkIpAddress(network)
                }
            }

            ""
        } catch (e: Exception) {
            logE(TAG, "Error getting current network IP address", e)
            ""
        }
    }


    @SuppressLint("MissingPermission")
    private fun isActuallyConnectedToTarget(targetSsid: String): Boolean {
        try {
            val currentSsid = getCurrentSsid()
            if (currentSsid != targetSsid) {
                logD(TAG, "SSID mismatch: current=$currentSsid, target=$targetSsid")
                return false
            }

            val wifiInfo = wifiManager.connectionInfo
            if (wifiInfo == null || wifiInfo.networkId == -1) {
                logD(TAG, "WiFi not connected or invalid network ID")
                return false
            }

            val activeNetwork = connectivityManager.activeNetwork
            if (activeNetwork == null) {
                logD(TAG, "No active network")
                return false
            }

            val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
            if (networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) != true) {
                logD(TAG, "Active network is not WiFi")
                return false
            }

            val ipAddress = getCurrentNetworkIpAddress()
            if (ipAddress.isEmpty() || !validateConnection(ipAddress)) {
                logD(TAG, "Invalid or empty IP address: $ipAddress")
                return false
            }

            currentNetwork = activeNetwork
            if (currentState != WifiClientState.CONNECTION_VALIDATED) {
                updateState(WifiClientState.CONNECTION_VALIDATED)
            }

            logD(TAG, "Actually connected to target network: $targetSsid, IP: $ipAddress")
            return true

        } catch (e: Exception) {
            logE(TAG, "Error checking actual connection status", e)
            return false
        }
    }

    private fun validateConnection(ipAddress: String): Boolean {
        if (ipAddress.isEmpty()) {
            logW(TAG, "IP address is empty")
            return false
        }

        // 检查IP地址格式
        try {
            val parts = ipAddress.split(".")
            if (parts.size != 4) return false
            parts.forEach {
                val num = it.toInt()
                if (num < 0 || num > 255) return false
            }
        } catch (e: Exception) {
            logE(TAG, "Invalid IP address format: $ipAddress", e)
            return false
        }

        return true
    }

    // ====================== [7. State Management] ======================
    private fun updateState(newState: WifiClientState) {
        logD(TAG, "State changed: $currentState -> $newState")
        currentState = newState
    }

    private fun reConnect() {
        if (targetSsid.isNotEmpty() &&
            currentState != WifiClientState.CONNECTED &&
            currentState != WifiClientState.CONNECTING
        ) {
            logD(TAG, "WiFi enabled, requesting WiFi info for reconnection")
            listener.requestWifiInfo()
        }
    }

    // ====================== [8. Event Handlers] ======================
    private fun handleWifiStateChanged(intent: Intent) {
        val state = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN)
        when (state) {
            WifiManager.WIFI_STATE_DISABLED -> {
                logD(TAG, "WiFi disabled")
                listener.onWifiState(false)
                updateState(WifiClientState.WIFI_DISABLED)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    activeNetworkCallback?.let {
                        try {
                            connectivityManager.unregisterNetworkCallback(it)
                        } catch (e: Exception) {
                            logE(TAG, "Error unregistering network callback", e)
                        }
                        activeNetworkCallback = null
                    }
                }
            }

            WifiManager.WIFI_STATE_ENABLED -> {
                logD(TAG, "WiFi enabled")
                listener.onWifiState(true)
                reConnect()
            }
        }
    }

    private fun handleNetworkStateChanged(intent: Intent) {
        logD(TAG,"wifi state ${wifiManager.wifiState}")
        val networkInfo = intent.getParcelableExtra<NetworkInfo>(WifiManager.EXTRA_NETWORK_INFO)
        if (networkInfo == null) {
            logW(TAG, "Network state changed: networkInfo is null")
            return
        }

        if (networkInfo.type != ConnectivityManager.TYPE_WIFI) {
            return
        }
        var wifiInfo = intent.getParcelableExtra<WifiInfo>(WifiManager.EXTRA_WIFI_INFO)
        if (wifiInfo == null) {
            wifiInfo = wifiManager.connectionInfo
        }
        if (wifiInfo == null) {
            logE(TAG, "Network state changed: wifiInfo is null")
            return
        }
        val ssid = wifiInfo.ssid?.replace("\"", "") ?: ""
        logD(TAG, "Network state changed: ${networkInfo.state} - ssid: $ssid")

        // 处理网络状态变化，同步内部状态
        when (networkInfo.state) {
            NetworkInfo.State.CONNECTED -> {
                // 网络已连接，检查是否是目标网络
                if (ssid == targetSsid && currentState != WifiClientState.CONNECTION_VALIDATED) {
                    logD(TAG, "Network connected to target SSID: $ssid")
                    // 验证连接并更新状态
                    val ipAddress = getCurrentNetworkIpAddress()
                    if (ipAddress.isNotEmpty() && validateConnection(ipAddress)) {
                        updateState(WifiClientState.CONNECTION_VALIDATED)
                        listener.onWifiConnected(ssid, ipAddress)
                    }
                }
            }

            NetworkInfo.State.DISCONNECTED -> {
                // 网络已断开
                if (ssid == targetSsid || targetSsid.isEmpty()) {
                    logD(TAG, "Network disconnected from SSID: $ssid")
                    if (currentState == WifiClientState.CONNECTION_VALIDATED ||
                        currentState == WifiClientState.CONNECTED
                    ) {
                        updateState(WifiClientState.DISCONNECTED)
                        listener.onWifiDisconnected()
                    }
                }
            }

            else -> {
                // 其他状态（连接中、暂停等）
                logD(TAG, "Network state: ${networkInfo.state} for SSID: $ssid")
            }
        }
    }
}

/**
 * WiFi客户端连接状态
 */
enum class WifiClientState {
    WIFI_CLIENT_START,      // 初始化状态
    CHECKING_PERMISSIONS,   // 检查权限
    PERMISSION_DENIED,      // 权限被拒绝
    WIFI_STATE_CHECK,       // 检查WiFi状态
    WIFI_DISABLED,          // WiFi未开启
    CONNECTING,             // 连接中
    CONNECTED,              // 已连接
    VALIDATING_CONNECTION,  // 验证连接
    CONNECTION_VALIDATED,   // 连接验证通过
    CONNECTION_INVALID,     // 连接无效
    DISCONNECTED,           // 已断开
    CONNECT_FAILED,         // 连接失败
    RECONNECTING,           // 重连中
    CONNECTION_FAILED       // 连接彻底失败
} 