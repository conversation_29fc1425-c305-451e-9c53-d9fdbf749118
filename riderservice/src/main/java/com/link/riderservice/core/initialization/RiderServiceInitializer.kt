package com.link.riderservice.core.initialization

import android.app.Application
import android.content.Context
import androidx.startup.Initializer
import com.link.riderservice.api.RiderService

/**
 * RiderService初始化器类，负责RiderService的初始化。
 */
class RiderServiceInitializer : Initializer<RiderService> {

    /**
     * 创建RiderService实例并进行初始化。
     * @param context 初始化上下文，提供了应用程序的环境。
     * @return 返回初始化后的RiderService单例。
     */
    override fun create(context: Context): RiderService {
        // 初始化RiderService单例
        RiderService.Companion.instance.init(context.applicationContext as Application)
        return RiderService.Companion.instance
    }

    /**
     * 返回此初始化器依赖的其他初始化器类列表。
     * @return 一个空列表，表示此初始化器没有依赖其他初始化器。
     */
    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }

    companion object {
        // 日志标签，用于日志输出
        private const val TAG = "RiderServiceInitializer"
    }
}