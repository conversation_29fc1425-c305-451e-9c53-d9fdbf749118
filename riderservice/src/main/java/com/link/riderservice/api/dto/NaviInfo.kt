package com.link.riderservice.api.dto

/**
 * 导航信息，发送给仪表盘
 * @property curLink 当前道路
 * @property curPoint 当前点
 * @property curStep 当前步骤
 * @property curStepRetainDistance 当前步骤剩余距离
 * @property curStepRetainTime 当前步骤剩余时间
 * @property naviType 导航类型
 * @property iconType 图标类型
 * @property pathRetainTime 路径剩余时间
 * @property pathRetainDistance 路径剩余距离
 * @property routeRemainLightCount 路径剩余红绿灯数量
 * @property pathId 路径id
 * @property nextRoadName 下一条道路名称
 * @property currentRoadName 当前道路名称
 * @property mapType 地图类型 默认为高德地图1
 * @property turnIconName 转向图标名称
 * @property turnKind 转向类型
 */
data class NaviInfo(
    val curLink: Int,
    val curPoint: Int,
    val curStep: Int,
    val curStepRetainDistance: Int,
    val curStepRetainTime: Int,
    val naviType: Int,
    val iconType: Int,
    val pathRetainTime: Int,
    val pathRetainDistance: Int,
    val routeRemainLightCount: Int,
    val pathId: Long,
    val nextRoadName: String,
    val currentRoadName: String,
    val mapType: Int,
    val turnIconName: String,
    val turnKind: String,
) : RiderMessage
