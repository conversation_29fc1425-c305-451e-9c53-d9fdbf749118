// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_IVIDEO_SOURCE_CALLBACKS_H
#define AUTOLINK_PROTOCOL_IVIDEO_SOURCE_CALLBACKS_H

#include "util/common.h"

/**
 * This class represents an interface that every source implementation must subclass if they
 * wish to implement an video source. 
 */
class IVideoSourceCallbacks {
public:
    virtual ~IVideoSourceCallbacks() = default;

    virtual int onChannelOpened() = 0;

    virtual int configCallback(
            int32_t status,
            uint32_t maxUnack,
            uint32_t *prefer, uint32_t size) = 0;

    virtual int ackCallback(int32_t sessionId, uint32_t numFrames) = 0;

    virtual int videoFocusNotifCallback(int32_t focus, bool unsolicited) = 0;

    virtual int displayChangeCallback(
            int32_t width,
            int32_t height,
            bool isLandscape,
            int32_t density) = 0;

    virtual bool discoverVideoConfigCallback(int32_t codec, int32_t fps, int32_t w, int32_t h) = 0;

    virtual int startResponseCallback(bool isOk) = 0;

    virtual int stopResponseCallback() = 0;


};


#endif // AUTOLINK_PROTOCOL_IVIDEO_SOURCE_CALLBACKS_H
