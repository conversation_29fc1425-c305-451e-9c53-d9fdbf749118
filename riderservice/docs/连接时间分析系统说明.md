# 简化连接时间跟踪系统

## 概述

简化的连接时间跟踪系统，只记录您需要的4种关键时间数据：

1. **蓝牙连接时间** - 从BLE连接开始到连接成功的时间
2. **WiFi连接时间** - 从WiFi连接开始到连接成功的时间（支持AP客户端模式和P2P模式）
3. **TCP连接时间** - 从TCP连接开始到连接成功的时间
4. **总连接时间** - 从蓝牙连接开始到TCP连接成功的完整流程时间

## 核心组件

### ConnectionTimeTracker
- **文件**: `ConnectionTimeTracker.kt`
- **功能**: 简单的连接时间跟踪器
- **特点**:
  - 只在Debug模式下工作
  - 自动记录每次完整连接的时间数据
  - 保存到cache目录的`connection_times.txt`文件

## 使用方法

### 1. 获取当前连接时间信息
```kotlin
val timeInfo = connectionManager.getConnectionTimeInfo()
println(timeInfo)
```

### 2. 清除记录
```kotlin
connectionManager.clearConnectionTimeRecords()
```

## 输出示例

### 控制台日志输出
```
=== 连接完成 ===
设备: 00:11:22:33:44:55
蓝牙连接时间: 1250ms
WiFi连接时间: 2100ms (AP_CLIENT)
TCP连接时间: 800ms
总连接时间: 4150ms
```

### 当前连接状态查询
```
=== 当前连接状态 ===
设备ID: 00:11:22:33:44:55
蓝牙连接时间: 1250ms
WiFi连接时间: 2100ms (AP_CLIENT)
TCP连接时间: 800ms
总连接时间: 4150ms
```

### 缓存文件格式 (connection_times.txt)
```
2024-01-01 10:30:15|00:11:22:33:44:55|BLE:1250ms|WiFi:2100ms(AP_CLIENT)|TCP:800ms|TOTAL:4150ms
2024-01-01 10:35:22|00:11:22:33:44:66|BLE:1180ms|WiFi:2350ms(P2P)|TCP:750ms|TOTAL:4280ms
```

## 工作原理

系统会自动在以下时间点记录数据：

1. **BLE连接开始**: 调用`startConnection(deviceId)`
2. **BLE连接成功**: 调用`recordBleConnected()`
3. **WiFi连接开始**: 调用`recordWifiConnecting(mode)`
4. **WiFi连接成功**: 调用`recordWifiConnected()`
5. **TCP连接开始**: 调用`recordTcpConnecting()`
6. **TCP连接成功**: 调用`recordTcpConnected()` - 完成整个流程并保存数据

## 配置

- **Debug模式限制**: 只在`BuildConfig.DEBUG = true`时工作
- **文件位置**: 保存到应用cache目录的`connection_times.txt`
- **自动记录**: 每次连接完成后自动保存，无需手动操作

## 简单易用

这个简化版本去除了复杂的会话管理、统计计算等功能，只专注于记录您需要的4种时间数据，使用简单，性能开销最小。
