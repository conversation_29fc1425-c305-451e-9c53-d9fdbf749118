# RiderService SDK 重构实现细节与调用示例

**版本**: 2.2 | **状态**: 详细实现 | **类型**: 架构重构 + Builder模式

---

## 目录

- [Builder模式集成](#builder模式集成)
- [完整接口定义](#完整接口定义)
- [核心组件实现](#核心组件实现)
- [WiFi双模式实现](#wifi双模式实现)
- [回调接口定义](#回调接口定义)
- [外部调用示例](#外部调用示例)
- [错误处理与状态管理](#错误处理与状态管理)
- [迁移指南](#迁移指南)

---

## Builder模式集成

### 1. RiderServiceBuilder 主构建器

```kotlin
class RiderServiceBuilder private constructor() {
    private var application: Application? = null
    private var debugMode: Boolean = false
    private var logLevel: LogLevel = LogLevel.INFO

    private var connectionConfigBuilder: ConnectionConfigBuilder = ConnectionConfigBuilder()
    private var messagingConfigBuilder: MessagingConfigBuilder = MessagingConfigBuilder()
    private var navigationConfigBuilder: NavigationConfigBuilder = NavigationConfigBuilder()
    private var projectionConfigBuilder: ProjectionConfigBuilder = ProjectionConfigBuilder()
    private var userPreferencesBuilder: UserPreferencesBuilder = UserPreferencesBuilder()

    companion object {
        /**
         * 创建Builder实例
         */
        fun newBuilder(): RiderServiceBuilder = RiderServiceBuilder()

        /**
         * 使用默认配置快速创建
         */
        fun createDefault(application: Application): RiderService {
            return newBuilder()
                .application(application)
                .build()
        }
    }

    // === 基础配置 ===
    fun application(application: Application): RiderServiceBuilder {
        this.application = application
        return this
    }

    fun debugMode(enabled: Boolean): RiderServiceBuilder {
        this.debugMode = enabled
        return this
    }

    fun logLevel(level: LogLevel): RiderServiceBuilder {
        this.logLevel = level
        return this
    }

    // === 连接配置 ===
    fun connection(block: ConnectionConfigBuilder.() -> Unit): RiderServiceBuilder {
        connectionConfigBuilder.apply(block)
        return this
    }

    // === 消息配置 ===
    fun messaging(block: MessagingConfigBuilder.() -> Unit): RiderServiceBuilder {
        messagingConfigBuilder.apply(block)
        return this
    }

    // === 导航配置 ===
    fun navigation(block: NavigationConfigBuilder.() -> Unit): RiderServiceBuilder {
        navigationConfigBuilder.apply(block)
        return this
    }

    // === 投屏配置 ===
    fun projection(block: ProjectionConfigBuilder.() -> Unit): RiderServiceBuilder {
        projectionConfigBuilder.apply(block)
        return this
    }

    // === 用户偏好 ===
    fun userPreferences(block: UserPreferencesBuilder.() -> Unit): RiderServiceBuilder {
        userPreferencesBuilder.apply(block)
        return this
    }

    /**
     * 构建RiderService实例
     */
    fun build(): RiderService {
        val app = application ?: throw IllegalStateException("Application is required")

        val config = RiderServiceConfig(
            application = app,
            debugMode = debugMode,
            logLevel = logLevel,
            connectionConfig = connectionConfigBuilder.build(),
            messagingConfig = messagingConfigBuilder.build(),
            navigationConfig = navigationConfigBuilder.build(),
            projectionConfig = projectionConfigBuilder.build(),
            userPreferences = userPreferencesBuilder.build()
        )

        // 验证配置
        val errors = config.validate()
        if (errors.isNotEmpty()) {
            throw IllegalArgumentException("Invalid configuration: ${errors.joinToString(", ")}")
        }

        return RiderServiceImpl.create(config)
    }
}

enum class LogLevel {
    VERBOSE, DEBUG, INFO, WARN, ERROR, NONE
}
```

### 2. 配置数据类

```kotlin
data class RiderServiceConfig(
    // === 基础配置 ===
    val application: Application,
    val debugMode: Boolean = false,
    val logLevel: LogLevel = LogLevel.INFO,

    // === 模块配置 ===
    val connectionConfig: ConnectionConfig = ConnectionConfig(),
    val messagingConfig: MessagingConfig = MessagingConfig(),
    val navigationConfig: NavigationConfig = NavigationConfig(),
    val projectionConfig: ProjectionConfig = ProjectionConfig(),
    val userPreferences: UserPreferences = UserPreferences()
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        errors.addAll(connectionConfig.validate())
        errors.addAll(messagingConfig.validate())
        errors.addAll(navigationConfig.validate())
        errors.addAll(projectionConfig.validate())
        errors.addAll(userPreferences.validate())
        return errors
    }
}

data class ConnectionConfig(
    val wifiMode: WifiConnectionMode = WifiConnectionMode.WIFI_AP_CLIENT,
    val wifiAutoConnect: Boolean = true,
    val wifiConnectionTimeout: Duration = 30.seconds,
    val bleScanTimeout: Duration = 10.seconds,
    val bleConnectionTimeout: Duration = 15.seconds,
    val bleAutoReconnect: Boolean = true,
    val retryPolicy: RetryPolicy = RetryPolicy(),
    val enableConnectionMetrics: Boolean = false
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (wifiConnectionTimeout.inWholeSeconds < 5) {
            errors.add("WiFi connection timeout must be at least 5 seconds")
        }
        if (bleScanTimeout.inWholeSeconds < 1) {
            errors.add("BLE scan timeout must be at least 1 second")
        }
        return errors
    }
}

data class MessagingConfig(
    val maxQueueSize: Int = 100,
    val messageTimeout: Duration = 5.seconds,
    val enableMessagePersistence: Boolean = false,
    val batchSending: Boolean = false,
    val batchSize: Int = 10,
    val batchInterval: Duration = 100.milliseconds,
    val enablePriorityQueue: Boolean = true,
    val highPriorityTypes: Set<String> = setOf("NaviInfo", "GpsSignal"),
    val enableCompression: Boolean = false,
    val compressionThreshold: Int = 1024 // bytes
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (maxQueueSize <= 0) {
            errors.add("Max queue size must be positive")
        }
        if (batchSize <= 0 || batchSize > maxQueueSize) {
            errors.add("Batch size must be positive and not exceed max queue size")
        }
        if (compressionThreshold <= 0) {
            errors.add("Compression threshold must be positive")
        }
        return errors
    }
}

data class NavigationConfig(
    // === 默认模式 ===
    val defaultMode: NaviMode = NaviMode.SimpleNavi,
    val autoStartNavigation: Boolean = false,

    // === 模式切换 ===
    val allowModeSwitch: Boolean = true,
    val modeSwitchTimeout: Duration = 10.seconds,

    // === 数据更新 ===
    val naviUpdateInterval: Duration = 1.seconds,
    val enableRouteOptimization: Boolean = true,

    // === 导航行为 ===
    val enableVoiceGuidance: Boolean = true,
    val enableLaneGuidance: Boolean = true,
    val enableSpeedLimit: Boolean = true,
    val enableTrafficInfo: Boolean = true,

    // === 显示设置 ===
    val mapOrientation: MapOrientation = MapOrientation.NORTH_UP,
    val zoomLevel: Int = 15,
    val enableNightMode: Boolean = false
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (modeSwitchTimeout.inWholeSeconds < 1) {
            errors.add("Mode switch timeout must be at least 1 second")
        }
        if (naviUpdateInterval.inWholeMilliseconds < 100) {
            errors.add("Navigation update interval must be at least 100ms")
        }
        if (zoomLevel < 1 || zoomLevel > 20) {
            errors.add("Zoom level must be between 1 and 20")
        }
        return errors
    }
}

enum class MapOrientation {
    NORTH_UP,
    HEADING_UP,
    COURSE_UP
}

data class ProjectionConfig(
    // === 显示配置 ===
    val enableAutoProjection: Boolean = false,
    val projectionQuality: ProjectionQuality = ProjectionQuality.MEDIUM,
    val frameRate: Int = 30,
    val resolution: Resolution = Resolution.HD_720P,

    // === 镜像配置 ===
    val enableMirrorMode: Boolean = true,
    val mirrorOrientation: MirrorOrientation = MirrorOrientation.AUTO,
    val enableAudioMirror: Boolean = false,

    // === 性能配置 ===
    val enableHardwareAcceleration: Boolean = true,
    val bufferSize: Int = 1024 * 1024, // 1MB
    val maxBitrate: Int = 8000, // kbps
    val enableAdaptiveBitrate: Boolean = true,

    // === 显示设置 ===
    val brightness: Float = 1.0f, // 0.0 - 1.0
    val contrast: Float = 1.0f,   // 0.0 - 2.0
    val saturation: Float = 1.0f, // 0.0 - 2.0

    // === 交互配置 ===
    val enableTouchInput: Boolean = true,
    val enableGestureControl: Boolean = false,
    val touchSensitivity: Float = 1.0f // 0.1 - 2.0
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (frameRate <= 0 || frameRate > 60) {
            errors.add("Frame rate must be between 1 and 60")
        }
        if (bufferSize <= 0) {
            errors.add("Buffer size must be positive")
        }
        if (maxBitrate <= 0) {
            errors.add("Max bitrate must be positive")
        }
        if (brightness < 0.0f || brightness > 1.0f) {
            errors.add("Brightness must be between 0.0 and 1.0")
        }
        if (contrast < 0.0f || contrast > 2.0f) {
            errors.add("Contrast must be between 0.0 and 2.0")
        }
        if (saturation < 0.0f || saturation > 2.0f) {
            errors.add("Saturation must be between 0.0 and 2.0")
        }
        if (touchSensitivity < 0.1f || touchSensitivity > 2.0f) {
            errors.add("Touch sensitivity must be between 0.1 and 2.0")
        }
        return errors
    }
}

enum class ProjectionQuality {
    LOW, MEDIUM, HIGH, ULTRA
}

enum class Resolution {
    HD_720P, FHD_1080P, QHD_1440P, UHD_4K
}

enum class MirrorOrientation {
    AUTO, PORTRAIT, LANDSCAPE, REVERSE_PORTRAIT, REVERSE_LANDSCAPE
}

data class UserPreferences(
    // === 导航偏好 ===
    val autoStartNavigation: Boolean = false,
    val defaultNavigationMode: NaviMode = NaviMode.SimpleNavi,
    val preferredMapStyle: MapStyle = MapStyle.STANDARD,

    // === 通知偏好 ===
    val enableNotifications: Boolean = true,
    val notificationTypes: Set<NotificationType> = setOf(
        NotificationType.NAVIGATION,
        NotificationType.TRAFFIC,
        NotificationType.WEATHER
    ),
    val notificationSound: Boolean = true,
    val notificationVibration: Boolean = true,

    // === 显示偏好 ===
    val theme: Theme = Theme.AUTO,
    val fontSize: FontSize = FontSize.MEDIUM,
    val language: String = "zh-CN",
    val units: Units = Units.METRIC,

    // === 连接偏好 ===
    val preferredWifiMode: WifiConnectionMode = WifiConnectionMode.WIFI_AP_CLIENT,
    val autoConnectLastDevice: Boolean = true,
    val connectionTimeout: Duration = 30.seconds,

    // === 隐私偏好 ===
    val enableAnalytics: Boolean = true,
    val enableCrashReporting: Boolean = true,
    val enableLocationSharing: Boolean = false,

    // === 性能偏好 ===
    val enablePowerSaving: Boolean = false,
    val backgroundDataUsage: DataUsageLevel = DataUsageLevel.NORMAL,
    val cacheSize: Int = 100 // MB
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (connectionTimeout.inWholeSeconds < 5) {
            errors.add("Connection timeout must be at least 5 seconds")
        }
        if (cacheSize <= 0 || cacheSize > 1000) {
            errors.add("Cache size must be between 1 and 1000 MB")
        }
        return errors
    }
}

enum class MapStyle {
    STANDARD, SATELLITE, TERRAIN, HYBRID
}

enum class NotificationType {
    NAVIGATION, TRAFFIC, WEATHER, SYSTEM, MESSAGE
}

enum class Theme {
    LIGHT, DARK, AUTO
}

enum class FontSize {
    SMALL, MEDIUM, LARGE, EXTRA_LARGE
}

enum class Units {
    METRIC, IMPERIAL
}

enum class DataUsageLevel {
    LOW, NORMAL, HIGH, UNLIMITED
}

data class RetryPolicy(
    val maxRetries: Int = 3,
    val initialDelayMs: Long = 1000,
    val maxDelayMs: Long = 10000,
    val backoffMultiplier: Double = 2.0,
    val enableJitter: Boolean = true
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (maxRetries < 0) {
            errors.add("Max retries must be non-negative")
        }
        if (initialDelayMs <= 0) {
            errors.add("Initial delay must be positive")
        }
        if (maxDelayMs <= initialDelayMs) {
            errors.add("Max delay must be greater than initial delay")
        }
        if (backoffMultiplier <= 1.0) {
            errors.add("Backoff multiplier must be greater than 1.0")
        }
        return errors
    }
}
```

---

## 完整接口定义

### 1. RiderService 主入口接口

```kotlin
interface RiderService {
    // === 管理器访问 ===
    val connection: ConnectionManager
    val messaging: MessagingManager
    val navigation: NavigationManager
    val projection: ProjectionManager
    val config: ConfigManager

    // === 生命周期管理 ===
    fun destroy()
    fun isInitialized(): Boolean

    // === 全局状态 ===
    val serviceState: StateFlow<ServiceState>

    // === 版本信息 ===
    fun getSdkVersion(): String
    fun getApiVersion(): String

    companion object {
        @Deprecated("Use RiderServiceBuilder instead", ReplaceWith("RiderServiceBuilder.getInstance()"))
        fun getInstance(): RiderService
    }
}

enum class ServiceState {
    UNINITIALIZED,
    INITIALIZING,
    INITIALIZED,
    ERROR,
    DESTROYED
}
```

### 2. ConnectionManager 连接管理接口

```kotlin
interface ConnectionManager {
    // === 设备扫描 ===
    suspend fun startBleScan(timeoutMs: Long = 10000): Result<List<BleDevice>>
    fun stopBleScan()
    fun isScanning(): Boolean

    // === 设备连接 ===
    suspend fun connect(device: BleDevice): Result<ConnectionInfo>
    suspend fun disconnect(): Result<Unit>
    fun isConnected(): Boolean
    fun getCurrentDevice(): BleDevice?

    // === WiFi连接管理 ===
    suspend fun connectWiFi(mode: WifiConnectionMode): Result<WifiConnectionInfo>
    fun requestWifiInfo(mode: WifiConnectionMode, isReset: Boolean = true)
    fun getWifiConnectionMode(): WifiConnectionMode
    fun setWifiConnectionMode(mode: WifiConnectionMode)

    // === 连接状态 ===
    fun getConnectionStatus(): Connection
    val connectionState: StateFlow<Connection>

    // === 事件监听 ===
    fun addConnectionListener(listener: ConnectionListener)
    fun removeConnectionListener(listener: ConnectionListener)
    fun clearAllListeners()
}
```

### 3. MessagingManager 消息管理接口

```kotlin
interface MessagingManager {
    // === 导航消息（保持现有数据结构）===
    fun sendNaviInfo(naviInfo: NaviInfo): Boolean
    fun sendNaviRoute(naviRoute: NaviRoute): Boolean
    fun sendLaneInfo(laneInfo: LaneInfo): Boolean
    fun sendNaviCross(naviCross: NaviCross): Boolean
    fun sendNaviText(naviText: NaviText): Boolean
    fun sendArriveDestination(): Boolean

    // === 系统消息（保持现有数据结构）===
    fun sendNotificationInfo(notificationInfo: NotificationInfo): Boolean
    fun sendWeatherInfo(weatherInfo: WeatherInfo): Boolean
    fun sendGpsSignal(gpsSignal: GpsSignal): Boolean

    // === 控制消息 ===
    fun sendNaviStart(): Boolean
    fun sendNaviStop(): Boolean
    fun sendAutoLinkConnect(autoLinkConnect: AutoLinkConnect): Boolean

    // === 消息状态 ===
    fun getMessageQueueSize(): Int
    fun clearMessageQueue()
    val queueSize: StateFlow<Int>
    val sendingState: StateFlow<MessageSendingState>

    // === 事件监听 ===
    fun addMessageListener(listener: MessageListener)
    fun removeMessageListener(listener: MessageListener)
}

enum class MessageSendingState {
    IDLE,
    SENDING,
    PAUSED,
    ERROR
}
```

### 4. NavigationManager 导航管理接口

```kotlin
interface NavigationManager {
    // === 导航模式控制 ===
    suspend fun startNavigation(mode: NaviMode): Result<Unit>
    suspend fun stopNavigation(mode: NaviMode): Result<Unit>
    suspend fun changeNavigationMode(newMode: NaviMode): Result<Unit>

    // === 导航状态 ===
    fun getCurrentNavigationMode(): NaviMode
    val navigationState: StateFlow<NavigationState>

    // === 事件监听 ===
    fun addNavigationListener(listener: NavigationListener)
    fun removeNavigationListener(listener: NavigationListener)
}

data class NavigationState(
    val currentMode: NaviMode,
    val isNavigating: Boolean,
    val lastUpdateTime: Long
)
```

### 5. ProjectionManager 投屏管理接口

```kotlin
interface ProjectionManager {
    // === 投屏控制 ===
    fun setMediaProjection(mediaProjection: MediaProjection)
    fun requestLockScreenDisplay()
    suspend fun startMirror(): Result<Unit>
    suspend fun stopMirror(): Result<Unit>

    // === 显示管理 ===
    fun getAvailableDisplays(): List<Display>
    val currentDisplay: StateFlow<Display?>

    // === 投屏状态 ===
    val projectionState: StateFlow<ProjectionState>

    // === 事件监听 ===
    fun addProjectionListener(listener: ProjectionListener)
    fun removeProjectionListener(listener: ProjectionListener)
}

data class ProjectionState(
    val isProjecting: Boolean,
    val projectionMode: ProjectionMode,
    val display: Display?
)

enum class ProjectionMode {
    NONE,
    MIRROR,
    PRESENTATION,
    LOCK_SCREEN
}
```

### 6. ConfigManager 配置管理接口

```kotlin
interface ConfigManager {
    // === 设备配置 ===
    suspend fun getDeviceInfo(): Result<DeviceInfo>
    suspend fun getDeviceConfig(): Result<DeviceConfig>

    // === 用户偏好 ===
    suspend fun getUserPreferences(): Result<UserPreferences>
    suspend fun updateUserPreferences(preferences: UserPreferences): Result<Unit>

    // === 版本信息 ===
    suspend fun getVersionInfo(): Result<VersionInfo>

    // === 配置状态 ===
    val configState: StateFlow<ConfigState>

    // === 事件监听 ===
    fun addConfigListener(listener: ConfigListener)
    fun removeConfigListener(listener: ConfigListener)
}

data class ConfigState(
    val isLoaded: Boolean,
    val deviceInfo: DeviceInfo?,
    val userPreferences: UserPreferences?
)
```

---

## 核心组件实现

### 1. ConnectionCore 连接核心逻辑（支持配置）

```kotlin
internal class ConnectionCore(
    private val config: ConnectionConfig
) {
    private val bleFeature = BleConnectionFeature()
    private val wifiApFeature = WiFiApConnectionFeature()
    private val wifiP2pFeature = WiFiP2pConnectionFeature()

    private val _connectionState = MutableStateFlow(Connection())
    val connectionState: StateFlow<Connection> = _connectionState.asStateFlow()

    private val listeners = mutableSetOf<WeakReference<ConnectionListener>>()
    private var currentWifiMode = config.wifiMode

    fun init(application: Application, messagingCore: MessagingCore) {
        // 使用配置初始化BLE功能
        bleFeature.init(application, config) { status ->
            handleBleStatusChange(status)
        }

        // 使用配置初始化WiFi AP功能
        wifiApFeature.init(application, config) { status ->
            handleWiFiApStatusChange(status)
        }

        // 使用配置初始化WiFi P2P功能
        wifiP2pFeature.init(application, config) { status ->
            handleWiFiP2pStatusChange(status)
        }

        // 应用连接配置
        applyConnectionConfig()
    }

    private fun applyConnectionConfig() {
        // 设置默认WiFi模式
        currentWifiMode = config.wifiMode

        // 配置自动重连
        if (config.bleAutoReconnect) {
            bleFeature.enableAutoReconnect(config.retryPolicy)
        }

        // 配置连接指标收集
        if (config.enableConnectionMetrics) {
            enableConnectionMetrics()
        }
    }

    suspend fun startBleScan(timeoutMs: Long = config.bleScanTimeout.inWholeMilliseconds): Result<List<BleDevice>> {
        return try {
            notifyListeners { it.onScanStarted() }
            val devices = bleFeature.startScan(timeoutMs)
            notifyListeners { it.onScanResult(devices) }
            notifyListeners { it.onScanFinished() }
            Result.success(devices)
        } catch (e: Exception) {
            notifyListeners { it.onConnectionError(ConnectionError.SCAN_FAILED, e.message) }
            Result.failure(e)
        }
    }

    suspend fun connect(device: BleDevice): Result<ConnectionInfo> {
        return try {
            notifyListeners { it.onConnectionStateChanged(ConnectionStatus.CONNECTING) }
            val connectionInfo = bleFeature.connect(device)
            updateConnectionState()
            notifyListeners { it.onDeviceConnected(device, connectionInfo) }
            Result.success(connectionInfo)
        } catch (e: Exception) {
            notifyListeners { it.onConnectionError(ConnectionError.CONNECTION_FAILED, e.message) }
            Result.failure(e)
        }
    }

    suspend fun connectWiFi(mode: WifiConnectionMode): Result<WifiConnectionInfo> {
        return try {
            currentWifiMode = mode
            val result = when (mode) {
                WifiConnectionMode.WIFI_AP_CLIENT -> wifiApFeature.connect()
                WifiConnectionMode.WIFI_P2P -> wifiP2pFeature.connect()
            }
            updateConnectionState()
            Result.success(result)
        } catch (e: Exception) {
            notifyListeners { it.onWifiConnectionFailed(mode, e.message ?: "Unknown error") }
            Result.failure(e)
        }
    }

    fun requestWifiInfo(mode: WifiConnectionMode, isReset: Boolean = true) {
        when (mode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> wifiApFeature.requestApInfo(isReset)
            WifiConnectionMode.WIFI_P2P -> wifiP2pFeature.requestP2pInfo(isReset)
        }
    }

    private fun handleBleStatusChange(status: BleStatus) {
        val currentState = _connectionState.value
        val newState = currentState.copy(btStatus = status)
        _connectionState.value = newState

        notifyListeners { listener ->
            when (status) {
                is BleStatus.DeviceConnected -> {
                    listener.onConnectionStateChanged(ConnectionStatus.CONNECTED)
                }
                is BleStatus.DeviceConnecting -> {
                    listener.onConnectionStateChanged(ConnectionStatus.CONNECTING)
                }
                is BleStatus.DeviceDisconnected -> {
                    listener.onConnectionStateChanged(ConnectionStatus.DISCONNECTED)
                    listener.onDeviceDisconnected(status.device, DisconnectReason.fromCode(status.reason))
                }
                is BleStatus.DeviceFailedToConnect -> {
                    listener.onConnectionError(ConnectionError.CONNECTION_FAILED, "Failed to connect: ${status.reason}")
                }
                else -> {
                    listener.onConnectionStateChanged(ConnectionStatus.DISCONNECTED)
                }
            }
        }
    }

    private fun handleWiFiApStatusChange(status: WiFiApStatus) {
        val currentState = _connectionState.value
        val newWifiStatus = if (status.isConnected) WifiStatus.DeviceConnected else WifiStatus.DeviceDisconnected
        val newState = currentState.copy(wifiStatus = newWifiStatus)
        _connectionState.value = newState

        notifyListeners { listener ->
            listener.onWifiStateChanged(status.isConnected)
            if (status.isConnected) {
                listener.onWifiConnected(WifiConnectionMode.WIFI_AP_CLIENT, status.connectionInfo)
            } else {
                listener.onWifiDisconnected(WifiConnectionMode.WIFI_AP_CLIENT)
            }
        }
    }

    private fun handleWiFiP2pStatusChange(status: WiFiP2pStatus) {
        // P2P状态处理逻辑
        notifyListeners { listener ->
            when (status.state) {
                WiFiP2pState.CONNECTED -> {
                    listener.onWifiConnected(WifiConnectionMode.WIFI_P2P, status.connectionInfo)
                }
                WiFiP2pState.DISCONNECTED -> {
                    listener.onWifiDisconnected(WifiConnectionMode.WIFI_P2P)
                }
                WiFiP2pState.FAILED -> {
                    listener.onWifiConnectionFailed(WifiConnectionMode.WIFI_P2P, status.errorMessage ?: "P2P connection failed")
                }
            }
        }
    }

    private fun updateConnectionState() {
        // 更新整体连接状态逻辑
    }

    private fun notifyListeners(action: (ConnectionListener) -> Unit) {
        listeners.removeAll { it.get() == null }
        listeners.forEach { ref ->
            ref.get()?.let(action)
        }
    }

    fun addListener(listener: ConnectionListener) {
        listeners.add(WeakReference(listener))
    }

    fun removeListener(listener: ConnectionListener) {
        listeners.removeAll { it.get() == listener }
    }

    fun clearAllListeners() {
        listeners.clear()
    }
}
```

### 2. MessagingCore 消息核心逻辑（支持配置）

```kotlin
internal class MessagingCore(
    private val config: MessagingConfig
) {
    private val messageQueue = MessageQueue(config)
    private val protocolFeature = ProtocolFeature()

    private val _queueSize = MutableStateFlow(0)
    val queueSize: StateFlow<Int> = _queueSize.asStateFlow()

    private val _sendingState = MutableStateFlow(MessageSendingState.IDLE)
    val sendingState: StateFlow<MessageSendingState> = _sendingState.asStateFlow()

    private val listeners = mutableSetOf<WeakReference<MessageListener>>()

    fun init(connectionCore: ConnectionCore) {
        protocolFeature.init { message ->
            handleIncomingMessage(message)
        }

        // 应用消息配置
        applyMessagingConfig()

        // 监听连接状态，控制消息发送
        connectionCore.connectionState.onEach { connection ->
            if (connection.isFullyConnected()) {
                resumeMessageSending()
            } else {
                pauseMessageSending()
            }
        }
    }

    private fun applyMessagingConfig() {
        // 配置消息队列
        messageQueue.setMaxSize(config.maxQueueSize)
        messageQueue.setTimeout(config.messageTimeout)

        // 配置批量发送
        if (config.batchSending) {
            messageQueue.enableBatchSending(config.batchSize)
        }

        // 配置优先级队列
        if (config.enablePriorityQueue) {
            messageQueue.enablePriorityQueue()
        }

        // 配置消息持久化
        if (config.enableMessagePersistence) {
            messageQueue.enablePersistence()
        }

        // 配置压缩
        if (config.enableCompression) {
            protocolFeature.enableCompression()
        }
    }

    fun sendNaviInfo(naviInfo: NaviInfo): Boolean {
        return sendMessage("NaviInfo") {
            protocolFeature.encodeNaviInfo(naviInfo)
        }
    }

    fun sendNotificationInfo(notificationInfo: NotificationInfo): Boolean {
        return sendMessage("NotificationInfo") {
            protocolFeature.encodeNotificationInfo(notificationInfo)
        }
    }

    fun sendWeatherInfo(weatherInfo: WeatherInfo): Boolean {
        return sendMessage("WeatherInfo") {
            protocolFeature.encodeWeatherInfo(weatherInfo)
        }
    }

    fun sendLaneInfo(laneInfo: LaneInfo): Boolean {
        return sendMessage("LaneInfo") {
            protocolFeature.encodeLaneInfo(laneInfo)
        }
    }

    fun sendNaviRoute(naviRoute: NaviRoute): Boolean {
        return sendMessage("NaviRoute") {
            protocolFeature.encodeNaviRoute(naviRoute)
        }
    }

    fun sendNaviCross(naviCross: NaviCross): Boolean {
        return sendMessage("NaviCross") {
            protocolFeature.encodeNaviCross(naviCross)
        }
    }

    fun sendNaviText(naviText: NaviText): Boolean {
        return sendMessage("NaviText") {
            protocolFeature.encodeNaviText(naviText)
        }
    }

    fun sendGpsSignal(gpsSignal: GpsSignal): Boolean {
        return sendMessage("GpsSignal") {
            protocolFeature.encodeGpsSignal(gpsSignal)
        }
    }

    fun sendArriveDestination(): Boolean {
        return sendMessage("ArriveDestination") {
            protocolFeature.encodeArriveDestination()
        }
    }

    fun sendNaviStart(): Boolean {
        return sendMessage("NaviStart") {
            protocolFeature.encodeNaviStart()
        }
    }

    fun sendNaviStop(): Boolean {
        return sendMessage("NaviStop") {
            protocolFeature.encodeNaviStop()
        }
    }

    fun sendAutoLinkConnect(autoLinkConnect: AutoLinkConnect): Boolean {
        return sendMessage("AutoLinkConnect") {
            protocolFeature.encodeAutoLinkConnect(autoLinkConnect)
        }
    }

    private fun sendMessage(messageType: String, encoder: () -> ByteArray): Boolean {
        return try {
            _sendingState.value = MessageSendingState.SENDING
            val protocolMessage = encoder()
            messageQueue.enqueue(MessageWrapper(messageType, protocolMessage))
            _queueSize.value = messageQueue.size()

            notifyListeners { it.onMessageSent(messageType, true) }
            _sendingState.value = MessageSendingState.IDLE
            true
        } catch (e: Exception) {
            notifyListeners { it.onMessageError("Failed to send $messageType: ${e.message}") }
            _sendingState.value = MessageSendingState.ERROR
            false
        }
    }

    private fun handleIncomingMessage(message: Any) {
        // 处理接收到的消息
        when (message) {
            is NaviModeChangeMessage -> {
                notifyListeners { it.onNaviModeChangeReceived(message.mode) }
            }
            is ConfigChangeMessage -> {
                notifyListeners { it.onConfigChangeReceived(message.config) }
            }
            is WeatherRequestMessage -> {
                notifyListeners { it.onWeatherInfoRequested() }
            }
        }
    }

    private fun resumeMessageSending() {
        if (_sendingState.value == MessageSendingState.PAUSED) {
            _sendingState.value = MessageSendingState.IDLE
            messageQueue.resume()
        }
    }

    private fun pauseMessageSending() {
        if (_sendingState.value != MessageSendingState.ERROR) {
            _sendingState.value = MessageSendingState.PAUSED
            messageQueue.pause()
        }
    }

    fun getQueueSize(): Int = messageQueue.size()

    fun clearQueue() {
        messageQueue.clear()
        _queueSize.value = 0
    }

    private fun notifyListeners(action: (MessageListener) -> Unit) {
        listeners.removeAll { it.get() == null }
        listeners.forEach { ref ->
            ref.get()?.let(action)
        }
    }

    fun addListener(listener: MessageListener) {
        listeners.add(WeakReference(listener))
    }

    fun removeListener(listener: MessageListener) {
        listeners.removeAll { it.get() == listener }
    }
}

data class MessageWrapper(
    val type: String,
    val data: ByteArray,
    val timestamp: Long = System.currentTimeMillis(),
    val priority: MessagePriority = MessagePriority.NORMAL
)

enum class MessagePriority {
    HIGH,
    NORMAL,
    LOW
}
```

---

## WiFi双模式实现

### 1. WiFiApConnectionFeature - AP客户端模式实现

```kotlin
internal class WiFiApConnectionFeature {
    private var wifiClientManager: WiFiClientManager? = null
    private var statusCallback: ((WiFiApStatus) -> Unit)? = null
    private var isConnected = false

    fun init(application: Application, callback: (WiFiApStatus) -> Unit) {
        this.statusCallback = callback
        wifiClientManager = WiFiClientManager(application).apply {
            setConnectionCallback(object : WiFiClientManager.ConnectionCallback {
                override fun onConnected(ssid: String, ipAddress: String) {
                    isConnected = true
                    val connectionInfo = WifiConnectionInfo(
                        mode = WifiConnectionMode.WIFI_AP_CLIENT,
                        ssid = ssid,
                        ipAddress = ipAddress,
                        timestamp = System.currentTimeMillis()
                    )
                    statusCallback?.invoke(WiFiApStatus(true, connectionInfo))
                }

                override fun onDisconnected() {
                    isConnected = false
                    statusCallback?.invoke(WiFiApStatus(false, null))
                }

                override fun onConnectionFailed(reason: String) {
                    isConnected = false
                    statusCallback?.invoke(WiFiApStatus(false, null, reason))
                }
            })
        }
    }

    suspend fun connect(): WifiConnectionInfo = withContext(Dispatchers.IO) {
        // 1. 通过BLE获取WiFi热点信息
        val wifiInfo = requestWifiInfoFromBle()

        // 2. 连接到WiFi热点
        val success = wifiClientManager?.connectToAp(wifiInfo.ssid, wifiInfo.password) ?: false

        if (success) {
            // 3. 等待连接完成
            delay(3000) // 等待连接建立

            // 4. 获取连接信息
            val ipAddress = wifiClientManager?.getIpAddress() ?: ""
            WifiConnectionInfo(
                mode = WifiConnectionMode.WIFI_AP_CLIENT,
                ssid = wifiInfo.ssid,
                ipAddress = ipAddress,
                timestamp = System.currentTimeMillis()
            )
        } else {
            throw Exception("Failed to connect to WiFi AP")
        }
    }

    fun requestApInfo(isReset: Boolean = true) {
        // 通过BLE请求WiFi热点信息
        wifiClientManager?.requestApInfo(isReset)
    }

    private suspend fun requestWifiInfoFromBle(): WifiInfo {
        // 通过BLE协议请求WiFi信息的实现
        return WifiInfo("RiderAP_12345", "password123")
    }

    fun disconnect() {
        wifiClientManager?.disconnect()
        isConnected = false
    }

    fun isConnected(): Boolean = isConnected
}

data class WiFiApStatus(
    val isConnected: Boolean,
    val connectionInfo: WifiConnectionInfo?,
    val errorMessage: String? = null
)

data class WifiInfo(
    val ssid: String,
    val password: String
)
```

### 2. WiFiP2pConnectionFeature - P2P直连模式实现

```kotlin
internal class WiFiP2pConnectionFeature {
    private var softP2pManager: SoftP2pManager? = null
    private var statusCallback: ((WiFiP2pStatus) -> Unit)? = null
    private var isConnected = false

    fun init(application: Application, callback: (WiFiP2pStatus) -> Unit) {
        this.statusCallback = callback
        softP2pManager = SoftP2pManager(application).apply {
            setP2pCallback(object : SoftP2pManager.P2pCallback {
                override fun onP2pConnected(peerAddress: String) {
                    isConnected = true
                    val connectionInfo = WifiConnectionInfo(
                        mode = WifiConnectionMode.WIFI_P2P,
                        peerAddress = peerAddress,
                        timestamp = System.currentTimeMillis()
                    )
                    statusCallback?.invoke(WiFiP2pStatus(WiFiP2pState.CONNECTED, connectionInfo))
                }

                override fun onP2pDisconnected() {
                    isConnected = false
                    statusCallback?.invoke(WiFiP2pStatus(WiFiP2pState.DISCONNECTED, null))
                }

                override fun onP2pConnectionFailed(reason: String) {
                    isConnected = false
                    statusCallback?.invoke(WiFiP2pStatus(WiFiP2pState.FAILED, null, reason))
                }

                override fun onP2pDiscoveryStarted() {
                    statusCallback?.invoke(WiFiP2pStatus(WiFiP2pState.DISCOVERING, null))
                }
            })
        }
    }

    suspend fun connect(): WifiConnectionInfo = withContext(Dispatchers.IO) {
        // 1. 启动P2P发现
        softP2pManager?.startDiscovery()

        // 2. 等待发现设备
        delay(5000)

        // 3. 连接到目标设备
        val success = softP2pManager?.connectToDevice() ?: false

        if (success) {
            // 4. 等待连接建立
            delay(3000)

            // 5. 获取连接信息
            val peerAddress = softP2pManager?.getPeerAddress() ?: ""
            WifiConnectionInfo(
                mode = WifiConnectionMode.WIFI_P2P,
                peerAddress = peerAddress,
                timestamp = System.currentTimeMillis()
            )
        } else {
            throw Exception("Failed to establish P2P connection")
        }
    }

    fun requestP2pInfo(isReset: Boolean = true) {
        // P2P模式下的信息请求
        softP2pManager?.requestConnectionInfo(isReset)
    }

    fun disconnect() {
        softP2pManager?.disconnect()
        isConnected = false
    }

    fun isConnected(): Boolean = isConnected
}

data class WiFiP2pStatus(
    val state: WiFiP2pState,
    val connectionInfo: WifiConnectionInfo?,
    val errorMessage: String? = null
)

enum class WiFiP2pState {
    IDLE,
    DISCOVERING,
    CONNECTING,
    CONNECTED,
    DISCONNECTED,
    FAILED
}

data class WifiConnectionInfo(
    val mode: WifiConnectionMode,
    val ssid: String = "",
    val ipAddress: String = "",
    val peerAddress: String = "",
    val timestamp: Long = System.currentTimeMillis()
)
```

---

## 回调接口定义

### 1. ConnectionListener - 连接事件监听器

```kotlin
interface ConnectionListener {
    // === 扫描事件 ===
    fun onScanStarted() {}
    fun onScanResult(devices: List<BleDevice>) {}
    fun onScanFinished() {}

    // === 连接状态事件 ===
    fun onConnectionStateChanged(status: ConnectionStatus) {}
    fun onDeviceConnected(device: BleDevice, info: ConnectionInfo) {}
    fun onDeviceDisconnected(device: BleDevice, reason: DisconnectReason) {}

    // === WiFi连接事件 ===
    fun onWifiStateChanged(isConnected: Boolean) {}
    fun onWifiConnected(mode: WifiConnectionMode, info: WifiConnectionInfo) {}
    fun onWifiDisconnected(mode: WifiConnectionMode) {}
    fun onWifiConnectionFailed(mode: WifiConnectionMode, error: String) {}

    // === 权限和错误事件 ===
    fun onNeedBluetoothScanPermission() {}
    fun onRequestOpenBluetooth() {}
    fun onNeedLocationPermission() {}
    fun onConnectionError(error: ConnectionError, message: String?) {}
    fun onSignalStrengthChanged(strength: Int) {}
}

enum class ConnectionStatus {
    DISCONNECTED,
    SCANNING,
    CONNECTING,
    CONNECTED,
    RECONNECTING,
    ERROR
}

enum class DisconnectReason {
    USER_REQUESTED,
    CONNECTION_LOST,
    DEVICE_NOT_FOUND,
    PERMISSION_DENIED,
    TIMEOUT,
    UNKNOWN;

    companion object {
        fun fromCode(code: Int): DisconnectReason {
            return when (code) {
                0 -> USER_REQUESTED
                1 -> CONNECTION_LOST
                2 -> DEVICE_NOT_FOUND
                3 -> PERMISSION_DENIED
                4 -> TIMEOUT
                else -> UNKNOWN
            }
        }
    }
}

enum class ConnectionError {
    SCAN_FAILED,
    CONNECTION_FAILED,
    PERMISSION_DENIED,
    BLUETOOTH_DISABLED,
    WIFI_DISABLED,
    TIMEOUT,
    UNKNOWN
}

data class ConnectionInfo(
    val device: BleDevice,
    val connectionTime: Long,
    val signalStrength: Int,
    val protocolVersion: String = "1.0"
)
```

### 2. MessageListener - 消息事件监听器

```kotlin
interface MessageListener {
    // === 消息发送事件 ===
    fun onMessageSent(messageType: String, success: Boolean) {}
    fun onMessageError(error: String) {}
    fun onQueueSizeChanged(size: Int) {}
    fun onSendingStateChanged(state: MessageSendingState) {}

    // === 接收消息事件 ===
    fun onNaviModeChangeReceived(mode: NaviMode) {}
    fun onConfigChangeReceived(config: RiderServiceConfig) {}
    fun onWeatherInfoRequested() {}
    fun onVersionRequested() {}
}
```

### 3. NavigationListener - 导航事件监听器

```kotlin
interface NavigationListener {
    // === 导航模式事件 ===
    fun onNavigationModeChanged(oldMode: NaviMode, newMode: NaviMode) {}
    fun onNavigationModeChangeResponse(mode: NaviMode, isReady: Boolean) {}
    fun onNavigationStarted(mode: NaviMode) {}
    fun onNavigationStopped(mode: NaviMode) {}

    // === 导航状态事件 ===
    fun onNavigationStateChanged(state: NavigationState) {}
    fun onNavigationError(error: String) {}
}
```

### 4. ProjectionListener - 投屏事件监听器

```kotlin
interface ProjectionListener {
    // === 显示事件 ===
    fun onDisplayInitialized(display: Display) {}
    fun onDisplayReleased(display: Display) {}
    fun onVideoChannelReady() {}

    // === 镜像事件 ===
    fun onRequestMediaProjection() {}
    fun onMirrorStarted() {}
    fun onMirrorStopped() {}

    // === 投屏状态事件 ===
    fun onProjectionStateChanged(state: ProjectionState) {}
    fun onProjectionError(error: String) {}
}
```

### 5. ConfigListener - 配置事件监听器

```kotlin
interface ConfigListener {
    // === 配置变更事件 ===
    fun onConfigChanged(config: RiderServiceConfig) {}
    fun onUserPreferencesChanged(preferences: UserPreferences) {}
    fun onDeviceInfoUpdated(deviceInfo: DeviceInfo) {}

    // === 配置状态事件 ===
    fun onConfigStateChanged(state: ConfigState) {}
    fun onConfigError(error: String) {}
}
```

---

## 外部调用示例

### 1. 基础初始化和连接示例（使用Builder模式）

```kotlin
class MainActivity : AppCompatActivity() {
    private lateinit var riderService: RiderService
    private val connectionListener = MyConnectionListener()
    private val messageListener = MyMessageListener()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // === 方式1：使用默认配置 ===
        riderService = RiderServiceBuilder.createDefault(application)

        // === 方式2：自定义配置 ===
        riderService = RiderServiceBuilder.newBuilder()
            .application(application)
            .debugMode(BuildConfig.DEBUG)
            .logLevel(LogLevel.DEBUG)
            .connection {
                wifiMode(WifiConnectionMode.WIFI_AP_CLIENT)
                wifiAutoConnect(true)
                wifiTimeout(30.seconds)
                bleTimeout(scanTimeout = 15.seconds, connectionTimeout = 20.seconds)
                autoReconnect(true)
            }
            .messaging {
                queueSize(200)
                enablePriorityQueue(true)
                batchSending(enabled = true, batchSize = 5)
                enableCompression(true)
                enablePersistence(true)
            }
            .navigation {
                defaultMode(NaviMode.SimpleNavi)
                enableVoiceGuidance(true)
                enableLaneGuidance(true)
                mapOrientation(MapOrientation.HEADING_UP)
                zoomLevel(16)
            }
            .projection {
                quality(ProjectionQuality.HIGH)
                frameRate(60)
                resolution(Resolution.FHD_1080P)
                enableMirror(true)
            }
            .userPreferences {
                autoStartNavigation(false)
                preferredMapStyle(MapStyle.STANDARD)
                notifications(enabled = true)
                displaySettings(theme = Theme.AUTO, fontSize = FontSize.MEDIUM, language = "zh-CN")
            }
            .build()

        setupListeners()
        startConnection()
    }

    private fun setupListeners() {
        // 3. 注册监听器
        riderService.connection.addConnectionListener(connectionListener)
        riderService.messaging.addMessageListener(messageListener)
    }

    private fun startConnection() {
        lifecycleScope.launch {
            try {
                // 4. 开始BLE扫描
                val scanResult = riderService.connection.startBleScan(10000)
                if (scanResult.isSuccess) {
                    val devices = scanResult.getOrNull() ?: emptyList()
                    if (devices.isNotEmpty()) {
                        // 5. 连接到第一个设备
                        connectToDevice(devices.first())
                    }
                }
            } catch (e: Exception) {
                Log.e("MainActivity", "Scan failed", e)
            }
        }
    }

    private suspend fun connectToDevice(device: BleDevice) {
        // 6. 连接BLE设备
        val connectResult = riderService.connection.connect(device)
        if (connectResult.isSuccess) {
            Log.d("MainActivity", "BLE connected successfully")

            // 7. 设置WiFi连接模式
            riderService.connection.setWifiConnectionMode(WifiConnectionMode.WIFI_AP_CLIENT)

            // 8. 连接WiFi
            val wifiResult = riderService.connection.connectWiFi(WifiConnectionMode.WIFI_AP_CLIENT)
            if (wifiResult.isSuccess) {
                Log.d("MainActivity", "WiFi connected successfully")
                startNavigation()
            }
        }
    }

    private suspend fun startNavigation() {
        // 9. 启动导航模式
        val naviResult = riderService.navigation.startNavigation(NaviMode.SimpleNavi)
        if (naviResult.isSuccess) {
            Log.d("MainActivity", "Navigation started")
            sendNavigationData()
        }
    }

    private fun sendNavigationData() {
        // 10. 发送导航信息
        val naviInfo = NaviInfo(
            curLink = 1,
            curPoint = 1,
            curStep = 1,
            curStepRetainDistance = 500,
            curStepRetainTime = 60,
            naviType = 1,
            iconType = 1,
            pathRetainTime = 1800,
            pathRetainDistance = 5000,
            routeRemainLightCount = 3,
            pathId = 12345L,
            nextRoadName = "中山大道",
            currentRoadName = "解放路",
            mapType = 1,
            turnIconName = "turn_left",
            turnKind = "左转"
        )

        val success = riderService.messaging.sendNaviInfo(naviInfo)
        Log.d("MainActivity", "Send NaviInfo: $success")
    }

    override fun onDestroy() {
        super.onDestroy()
        // 11. 清理资源
        riderService.connection.removeConnectionListener(connectionListener)
        riderService.messaging.removeMessageListener(messageListener)
        riderService.destroy()
    }
}
```

### 2. 连接监听器实现示例

```kotlin
class MyConnectionListener : ConnectionListener {
    override fun onScanResult(devices: List<BleDevice>) {
        Log.d("Connection", "Found ${devices.size} devices")
        devices.forEach { device ->
            Log.d("Connection", "Device: ${device.name} - ${device.address}")
        }
    }

    override fun onConnectionStateChanged(status: ConnectionStatus) {
        Log.d("Connection", "Connection status: $status")
        when (status) {
            ConnectionStatus.CONNECTED -> {
                Log.i("Connection", "Device connected successfully")
            }
            ConnectionStatus.DISCONNECTED -> {
                Log.w("Connection", "Device disconnected")
            }
            ConnectionStatus.CONNECTING -> {
                Log.i("Connection", "Connecting to device...")
            }
            ConnectionStatus.ERROR -> {
                Log.e("Connection", "Connection error occurred")
            }
            else -> {
                Log.d("Connection", "Status: $status")
            }
        }
    }

    override fun onDeviceConnected(device: BleDevice, info: ConnectionInfo) {
        Log.i("Connection", "Device connected: ${device.name}")
        Log.d("Connection", "Connection info: $info")
    }

    override fun onWifiConnected(mode: WifiConnectionMode, info: WifiConnectionInfo) {
        Log.i("Connection", "WiFi connected with mode: $mode")
        Log.d("Connection", "WiFi info: $info")
    }

    override fun onWifiConnectionFailed(mode: WifiConnectionMode, error: String) {
        Log.e("Connection", "WiFi connection failed for mode $mode: $error")
    }

    override fun onNeedBluetoothScanPermission() {
        Log.w("Connection", "Bluetooth scan permission required")
        // 请求蓝牙扫描权限
    }

    override fun onRequestOpenBluetooth() {
        Log.w("Connection", "Bluetooth needs to be enabled")
        // 请求打开蓝牙
    }

    override fun onConnectionError(error: ConnectionError, message: String?) {
        Log.e("Connection", "Connection error: $error - $message")
    }
}
```

### 3. 消息监听器实现示例

```kotlin
class MyMessageListener : MessageListener {
    override fun onMessageSent(messageType: String, success: Boolean) {
        Log.d("Message", "Message sent: $messageType, success: $success")
    }

    override fun onMessageError(error: String) {
        Log.e("Message", "Message error: $error")
    }

    override fun onQueueSizeChanged(size: Int) {
        Log.d("Message", "Queue size changed: $size")
    }

    override fun onNaviModeChangeReceived(mode: NaviMode) {
        Log.i("Message", "Navigation mode change received: $mode")
        // 处理导航模式变更
    }

    override fun onWeatherInfoRequested() {
        Log.i("Message", "Weather info requested")
        // 发送天气信息
        sendWeatherInfo()
    }

    private fun sendWeatherInfo() {
        val weatherInfo = WeatherInfo(
            wea = "晴",
            tem = "25",
            humidity = "60",
            pressure = "1013",
            altitude = "100"
        )
        // 注意：这里应该通过依赖注入或其他方式获取RiderService实例
        // 而不是直接调用getInstance()，因为现在使用Builder模式创建
    }
}
```

### 4. 完整的导航应用示例

```kotlin
class NavigationActivity : AppCompatActivity() {
    private lateinit var riderService: RiderService
    private val navigationListener = MyNavigationListener()
    private val projectionListener = MyProjectionListener()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 使用Builder模式创建RiderService实例
        riderService = RiderServiceBuilder.newBuilder()
            .application(application)
            .debugMode(BuildConfig.DEBUG)
            .navigation {
                defaultMode(NaviMode.SimpleNavi)
                autoStart(false)
            }
            .projection {
                autoProjection(false)
                quality(ProjectionQuality.HIGH)
            }
            .build()
        setupNavigationListeners()

        // 监听连接状态
        lifecycleScope.launch {
            riderService.connection.connectionState.collect { connection ->
                if (connection.isFullyConnected()) {
                    onConnectionReady()
                }
            }
        }
    }

    private fun setupNavigationListeners() {
        riderService.navigation.addNavigationListener(navigationListener)
        riderService.projection.addProjectionListener(projectionListener)
    }

    private suspend fun onConnectionReady() {
        // 连接就绪后的处理
        Log.d("Navigation", "Connection ready, starting navigation setup")

        // 1. 启动简易导航模式
        startSimpleNavigation()

        // 2. 发送初始导航数据
        sendInitialNavigationData()
    }

    private suspend fun startSimpleNavigation() {
        val result = riderService.navigation.startNavigation(NaviMode.SimpleNavi)
        if (result.isSuccess) {
            Log.d("Navigation", "Simple navigation started")
        } else {
            Log.e("Navigation", "Failed to start navigation: ${result.exceptionOrNull()}")
        }
    }

    private fun sendInitialNavigationData() {
        // 发送导航开始信号
        riderService.messaging.sendNaviStart()

        // 发送车道信息
        val laneInfo = LaneInfo(
            backgroundLane = intArrayOf(1, 1, 1),
            frontLane = intArrayOf(0, 1, 0),
            laneCount = 3
        )
        riderService.messaging.sendLaneInfo(laneInfo)

        // 发送导航文本
        val naviText = NaviText(
            type = 1,
            text = "请保持直行500米"
        )
        riderService.messaging.sendNaviText(naviText)
    }

    // 切换到投屏模式
    private suspend fun switchToProjectionMode() {
        // 1. 停止当前导航模式
        riderService.navigation.stopNavigation(NaviMode.SimpleNavi)

        // 2. 启动投屏模式
        val result = riderService.navigation.startNavigation(NaviMode.ScreenNavi)
        if (result.isSuccess) {
            Log.d("Navigation", "Switched to projection mode")

            // 3. 请求媒体投影权限
            requestMediaProjectionPermission()
        }
    }

    private fun requestMediaProjectionPermission() {
        // 请求媒体投影权限的实现
        val mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        val intent = mediaProjectionManager.createScreenCaptureIntent()
        startActivityForResult(intent, REQUEST_MEDIA_PROJECTION)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_MEDIA_PROJECTION && resultCode == RESULT_OK) {
            val mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            val mediaProjection = mediaProjectionManager.getMediaProjection(resultCode, data!!)

            // 设置媒体投影
            riderService.projection.setMediaProjection(mediaProjection)
        }
    }

    companion object {
        private const val REQUEST_MEDIA_PROJECTION = 1001
    }
}

class MyNavigationListener : NavigationListener {
    override fun onNavigationModeChanged(oldMode: NaviMode, newMode: NaviMode) {
        Log.i("Navigation", "Mode changed from $oldMode to $newMode")
    }

    override fun onNavigationModeChangeResponse(mode: NaviMode, isReady: Boolean) {
        Log.i("Navigation", "Mode change response: $mode, ready: $isReady")
    }
}

class MyProjectionListener : ProjectionListener {
    override fun onDisplayInitialized(display: Display) {
        Log.i("Projection", "Display initialized: ${display.displayId}")
    }

    override fun onRequestMediaProjection() {
        Log.i("Projection", "Media projection requested")
    }

    override fun onMirrorStarted() {
        Log.i("Projection", "Mirror started")
    }
}
```

---

## 错误处理与状态管理

### 1. 统一错误处理机制

```kotlin
// 统一的Result类型处理
sealed class RiderServiceResult<out T> {
    data class Success<T>(val data: T) : RiderServiceResult<T>()
    data class Error(val exception: RiderServiceException) : RiderServiceResult<Nothing>()

    fun isSuccess(): Boolean = this is Success
    fun isError(): Boolean = this is Error

    fun getOrNull(): T? = when (this) {
        is Success -> data
        is Error -> null
    }

    fun exceptionOrNull(): RiderServiceException? = when (this) {
        is Success -> null
        is Error -> exception
    }
}

// 自定义异常类型
sealed class RiderServiceException(message: String, cause: Throwable? = null) : Exception(message, cause) {
    class InitializationException(message: String, cause: Throwable? = null) : RiderServiceException(message, cause)
    class ConnectionException(message: String, cause: Throwable? = null) : RiderServiceException(message, cause)
    class MessageException(message: String, cause: Throwable? = null) : RiderServiceException(message, cause)
    class NavigationException(message: String, cause: Throwable? = null) : RiderServiceException(message, cause)
    class ProjectionException(message: String, cause: Throwable? = null) : RiderServiceException(message, cause)
    class ConfigException(message: String, cause: Throwable? = null) : RiderServiceException(message, cause)
    class PermissionException(message: String, cause: Throwable? = null) : RiderServiceException(message, cause)
    class TimeoutException(message: String, cause: Throwable? = null) : RiderServiceException(message, cause)
}

// 错误处理工具类
object ErrorHandler {
    fun handleConnectionError(error: Throwable): RiderServiceException {
        return when (error) {
            is SecurityException -> RiderServiceException.PermissionException("Permission denied: ${error.message}", error)
            is TimeoutException -> RiderServiceException.TimeoutException("Operation timeout: ${error.message}", error)
            is IOException -> RiderServiceException.ConnectionException("Connection error: ${error.message}", error)
            else -> RiderServiceException.ConnectionException("Unknown connection error: ${error.message}", error)
        }
    }

    fun handleMessageError(error: Throwable): RiderServiceException {
        return when (error) {
            is IllegalArgumentException -> RiderServiceException.MessageException("Invalid message format: ${error.message}", error)
            is IllegalStateException -> RiderServiceException.MessageException("Invalid state for message sending: ${error.message}", error)
            else -> RiderServiceException.MessageException("Message error: ${error.message}", error)
        }
    }
}
```

### 2. 状态管理实现

```kotlin
// 全局状态管理器
internal class StateManager {
    private val _serviceState = MutableStateFlow(ServiceState.UNINITIALIZED)
    val serviceState: StateFlow<ServiceState> = _serviceState.asStateFlow()

    private val _connectionState = MutableStateFlow(Connection())
    val connectionState: StateFlow<Connection> = _connectionState.asStateFlow()

    private val _navigationState = MutableStateFlow(NavigationState(NaviMode.NoNavi, false, 0))
    val navigationState: StateFlow<NavigationState> = _navigationState.asStateFlow()

    private val _projectionState = MutableStateFlow(ProjectionState(false, ProjectionMode.NONE, null))
    val projectionState: StateFlow<ProjectionState> = _projectionState.asStateFlow()

    fun updateServiceState(state: ServiceState) {
        _serviceState.value = state
    }

    fun updateConnectionState(connection: Connection) {
        _connectionState.value = connection
    }

    fun updateNavigationState(state: NavigationState) {
        _navigationState.value = state
    }

    fun updateProjectionState(state: ProjectionState) {
        _projectionState.value = state
    }

    // 状态验证
    fun canStartNavigation(): Boolean {
        return _connectionState.value.isFullyConnected() &&
               _serviceState.value == ServiceState.INITIALIZED
    }

    fun canSendMessage(): Boolean {
        return _connectionState.value.isFullyConnected()
    }

    fun canStartProjection(): Boolean {
        return _connectionState.value.isFullyConnected() &&
               _navigationState.value.currentMode != NaviMode.NoNavi
    }
}

// 扩展函数
fun Connection.isFullyConnected(): Boolean {
    return btStatus is BleStatus.DeviceConnected &&
           wifiStatus is WifiStatus.DeviceConnected
}

fun Connection.isBleConnected(): Boolean {
    return btStatus is BleStatus.DeviceConnected
}

fun Connection.isWifiConnected(): Boolean {
    return wifiStatus is WifiStatus.DeviceConnected
}
```

### 3. 异步操作处理

```kotlin
// 异步操作管理器
internal class AsyncOperationManager {
    private val operationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private val activeOperations = mutableMapOf<String, Job>()

    fun <T> executeWithTimeout(
        operationId: String,
        timeoutMs: Long,
        operation: suspend () -> T
    ): Deferred<RiderServiceResult<T>> {
        return operationScope.async {
            try {
                // 取消之前的同类操作
                activeOperations[operationId]?.cancel()

                val job = async {
                    withTimeout(timeoutMs) {
                        operation()
                    }
                }

                activeOperations[operationId] = job
                val result = job.await()
                activeOperations.remove(operationId)

                RiderServiceResult.Success(result)
            } catch (e: TimeoutCancellationException) {
                activeOperations.remove(operationId)
                RiderServiceResult.Error(RiderServiceException.TimeoutException("Operation timeout: $operationId"))
            } catch (e: Exception) {
                activeOperations.remove(operationId)
                RiderServiceResult.Error(ErrorHandler.handleConnectionError(e))
            }
        }
    }

    fun cancelOperation(operationId: String) {
        activeOperations[operationId]?.cancel()
        activeOperations.remove(operationId)
    }

    fun cancelAllOperations() {
        activeOperations.values.forEach { it.cancel() }
        activeOperations.clear()
    }

    fun destroy() {
        cancelAllOperations()
        operationScope.cancel()
    }
}

// 重试机制
class RetryPolicy(
    val maxRetries: Int = 3,
    val initialDelayMs: Long = 1000,
    val maxDelayMs: Long = 10000,
    val backoffMultiplier: Double = 2.0
) {
    suspend fun <T> execute(operation: suspend () -> T): T {
        var lastException: Exception? = null
        var delay = initialDelayMs

        repeat(maxRetries + 1) { attempt ->
            try {
                return operation()
            } catch (e: Exception) {
                lastException = e
                if (attempt < maxRetries) {
                    delay(delay)
                    delay = (delay * backoffMultiplier).toLong().coerceAtMost(maxDelayMs)
                }
            }
        }

        throw lastException ?: Exception("Retry failed")
    }
}
```

### 4. 生命周期管理

```kotlin
// 生命周期管理器
internal class LifecycleManager {
    private var isInitialized = false
    private val components = mutableListOf<LifecycleComponent>()

    fun addComponent(component: LifecycleComponent) {
        components.add(component)
    }

    suspend fun initialize(application: Application): RiderServiceResult<Unit> {
        return try {
            if (isInitialized) {
                return RiderServiceResult.Success(Unit)
            }

            // 按顺序初始化组件
            components.forEach { component ->
                component.initialize(application)
            }

            isInitialized = true
            RiderServiceResult.Success(Unit)
        } catch (e: Exception) {
            RiderServiceResult.Error(RiderServiceException.InitializationException("Initialization failed", e))
        }
    }

    fun destroy() {
        if (!isInitialized) return

        // 反向顺序销毁组件
        components.reversed().forEach { component ->
            try {
                component.destroy()
            } catch (e: Exception) {
                Log.e("LifecycleManager", "Error destroying component: ${component::class.simpleName}", e)
            }
        }

        components.clear()
        isInitialized = false
    }

    fun isInitialized(): Boolean = isInitialized
}

// 生命周期组件接口
internal interface LifecycleComponent {
    suspend fun initialize(application: Application)
    fun destroy()
}

// 组件实现示例
internal class ConnectionCoreComponent(
    private val connectionCore: ConnectionCore
) : LifecycleComponent {

    override suspend fun initialize(application: Application) {
        connectionCore.init(application, messagingCore)
    }

    override fun destroy() {
        connectionCore.destroy()
    }
}
```

---

## 迁移指南

### 1. API迁移对照表

| 旧API | 新API | 说明 |
|-------|-------|------|
| `RiderService.startBleScan()` | `riderService.connection.startBleScan()` | 移至ConnectionManager |
| `RiderService.connect(device)` | `riderService.connection.connect(device)` | 移至ConnectionManager |
| `RiderService.sendMessageToRiderService(message)` | `riderService.messaging.sendXXX(message)` | 按消息类型分组 |
| `RiderService.sendNaviModeStart(mode)` | `riderService.navigation.startNavigation(mode)` | 移至NavigationManager |
| `RiderService.setMediaProjection(projection)` | `riderService.projection.setMediaProjection(projection)` | 移至ProjectionManager |
| `RiderService.addCallback(callback)` | 分组注册监听器 | 按功能分组 |

### 2. 回调接口迁移

```kotlin
// === 旧方式：单一巨型回调接口 ===
class OldCallback : RiderServiceCallback() {
    override fun onScanResult(devices: List<BleDevice>) {
        // 处理扫描结果
    }

    override fun onConnectStatusChange(status: Connection) {
        // 处理连接状态变化
    }

    override fun onDisplayInitialized(display: Display) {
        // 处理显示初始化
    }

    override fun onNaviModeChange(mode: NaviMode) {
        // 处理导航模式变化
    }

    // ... 需要实现25个方法
}

// 注册回调
riderService.addCallback(OldCallback())

// === 新方式：按功能分组的监听器 ===
class NewConnectionListener : ConnectionListener {
    override fun onScanResult(devices: List<BleDevice>) {
        // 处理扫描结果
    }

    override fun onConnectionStateChanged(status: ConnectionStatus) {
        // 处理连接状态变化
    }
}

class NewProjectionListener : ProjectionListener {
    override fun onDisplayInitialized(display: Display) {
        // 处理显示初始化
    }
}

class NewNavigationListener : NavigationListener {
    override fun onNavigationModeChanged(oldMode: NaviMode, newMode: NaviMode) {
        // 处理导航模式变化
    }
}

// 按需注册监听器
riderService.connection.addConnectionListener(NewConnectionListener())
riderService.projection.addProjectionListener(NewProjectionListener())
riderService.navigation.addNavigationListener(NewNavigationListener())
```

### 3. 消息发送迁移

```kotlin
// === 旧方式：统一消息发送 ===
// 发送导航信息
val naviInfo = NaviInfo(...)
riderService.sendMessageToRiderService(naviInfo)

// 发送通知信息
val notificationInfo = NotificationInfo(...)
riderService.sendMessageToRiderService(notificationInfo)

// 发送天气信息
val weatherInfo = WeatherInfo(...)
riderService.sendMessageToRiderService(weatherInfo)

// === 新方式：类型安全的专门方法 ===
// 发送导航信息
val naviInfo = NaviInfo(...)
val success = riderService.messaging.sendNaviInfo(naviInfo)

// 发送通知信息
val notificationInfo = NotificationInfo(...)
val success = riderService.messaging.sendNotificationInfo(notificationInfo)

// 发送天气信息
val weatherInfo = WeatherInfo(...)
val success = riderService.messaging.sendWeatherInfo(weatherInfo)
```

### 4. 异步操作迁移

```kotlin
// === 旧方式：回调方式 ===
riderService.startBleScan() // 异步操作，结果通过回调返回

class MyCallback : RiderServiceCallback() {
    override fun onScanResult(devices: List<BleDevice>) {
        // 处理扫描结果
        if (devices.isNotEmpty()) {
            riderService.connect(devices.first()) // 异步连接
        }
    }

    override fun onConnectStatusChange(status: Connection) {
        if (status.btStatus is BleStatus.DeviceConnected) {
            // 连接成功，继续后续操作
        }
    }
}

// === 新方式：协程方式 ===
lifecycleScope.launch {
    try {
        // 扫描设备
        val scanResult = riderService.connection.startBleScan(10000)
        if (scanResult.isSuccess) {
            val devices = scanResult.getOrNull() ?: emptyList()
            if (devices.isNotEmpty()) {
                // 连接设备
                val connectResult = riderService.connection.connect(devices.first())
                if (connectResult.isSuccess) {
                    // 连接成功，继续后续操作
                    Log.d("Migration", "Connected successfully")
                }
            }
        }
    } catch (e: Exception) {
        Log.e("Migration", "Operation failed", e)
    }
}
```

### 5. 状态监听迁移

```kotlin
// === 旧方式：通过回调监听状态 ===
class MyCallback : RiderServiceCallback() {
    override fun onConnectStatusChange(status: Connection) {
        // 处理连接状态变化
        updateUI(status)
    }
}

// === 新方式：通过StateFlow监听状态 ===
lifecycleScope.launch {
    riderService.connection.connectionState.collect { connection ->
        // 处理连接状态变化
        updateUI(connection)
    }
}

// 或者使用监听器方式
class MyConnectionListener : ConnectionListener {
    override fun onConnectionStateChanged(status: ConnectionStatus) {
        // 处理连接状态变化
        updateUI(status)
    }
}
```

### 6. 完整迁移示例

```kotlin
// === 旧代码示例 ===
class OldMainActivity : AppCompatActivity() {
    private lateinit var riderService: RiderService

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        riderService = RiderService.getInstance()
        riderService.init(application)
        riderService.addCallback(MyOldCallback())

        // 开始扫描
        riderService.startBleScan()
    }

    inner class MyOldCallback : RiderServiceCallback() {
        override fun onScanResult(devices: List<BleDevice>) {
            if (devices.isNotEmpty()) {
                riderService.connect(devices.first())
            }
        }

        override fun onConnectStatusChange(status: Connection) {
            if (status.btStatus is BleStatus.DeviceConnected) {
                // 发送导航信息
                val naviInfo = NaviInfo(...)
                riderService.sendMessageToRiderService(naviInfo)

                // 启动导航模式
                riderService.sendNaviModeStart(NaviMode.SimpleNavi)
            }
        }

        override fun onNaviModeChangeResponse(mode: NaviMode, isReady: Boolean) {
            if (isReady) {
                // 导航模式就绪
            }
        }
    }
}

// === 新代码示例 ===
class NewMainActivity : AppCompatActivity() {
    private lateinit var riderService: RiderService
    private val connectionListener = MyConnectionListener()
    private val navigationListener = MyNavigationListener()
    private val messageListener = MyMessageListener()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 使用Builder模式创建RiderService实例
        riderService = RiderServiceBuilder.newBuilder()
            .application(application)
            .debugMode(BuildConfig.DEBUG)
            .connection {
                wifiMode(WifiConnectionMode.WIFI_AP_CLIENT)
                autoReconnect(true)
            }
            .messaging {
                queueSize(200)
                enablePriorityQueue(true)
            }
            .build()

        setupListeners()
        startConnection()
    }

    private fun setupListeners() {
        riderService.connection.addConnectionListener(connectionListener)
        riderService.navigation.addNavigationListener(navigationListener)
        riderService.messaging.addMessageListener(messageListener)
    }

    private fun startConnection() {
        lifecycleScope.launch {
            // 开始扫描
            val scanResult = riderService.connection.startBleScan(10000)
            if (scanResult.isSuccess) {
                val devices = scanResult.getOrNull() ?: emptyList()
                if (devices.isNotEmpty()) {
                    connectAndStartNavigation(devices.first())
                }
            }
        }
    }

    private suspend fun connectAndStartNavigation(device: BleDevice) {
        // 连接设备
        val connectResult = riderService.connection.connect(device)
        if (connectResult.isSuccess) {
            // 发送导航信息
            val naviInfo = NaviInfo(...)
            riderService.messaging.sendNaviInfo(naviInfo)

            // 启动导航模式
            val naviResult = riderService.navigation.startNavigation(NaviMode.SimpleNavi)
            if (naviResult.isSuccess) {
                Log.d("NewMain", "Navigation started successfully")
            }
        }
    }

    inner class MyConnectionListener : ConnectionListener {
        override fun onConnectionStateChanged(status: ConnectionStatus) {
            Log.d("NewMain", "Connection status: $status")
        }
    }

    inner class MyNavigationListener : NavigationListener {
        override fun onNavigationModeChangeResponse(mode: NaviMode, isReady: Boolean) {
            if (isReady) {
                Log.d("NewMain", "Navigation mode ready: $mode")
            }
        }
    }

    inner class MyMessageListener : MessageListener {
        override fun onMessageSent(messageType: String, success: Boolean) {
            Log.d("NewMain", "Message sent: $messageType, success: $success")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        riderService.connection.removeConnectionListener(connectionListener)
        riderService.navigation.removeNavigationListener(navigationListener)
        riderService.messaging.removeMessageListener(messageListener)
        riderService.destroy()
    }
}
```

### 7. 迁移检查清单

**准备阶段:**
- [ ] 备份现有代码
- [ ] 了解新架构设计
- [ ] 识别需要迁移的功能

**接口迁移:**
- [ ] 将RiderService的直接方法调用改为管理器调用
- [ ] 将统一的消息发送改为类型安全的专门方法
- [ ] 将单一回调接口拆分为功能分组的监听器
- [ ] 使用Builder模式替换直接的init()调用

**异步处理:**
- [ ] 将回调方式改为协程方式
- [ ] 添加适当的错误处理
- [ ] 使用StateFlow监听状态变化

**测试验证:**
- [ ] 验证所有功能正常工作
- [ ] 检查性能是否有改善
- [ ] 确认内存泄漏问题已解决

**清理工作:**
- [ ] 移除旧的回调接口实现
- [ ] 更新文档和注释
- [ ] 进行代码审查

---

## 总结

这个详细的重构实现方案（集成Builder模式）提供了：

1. **Builder模式集成**: 配置集中化、类型安全的初始化方式
2. **完整的接口定义**: 清晰的API设计，职责分离明确
3. **核心组件实现**: 支持配置的业务逻辑实现，包括连接管理和消息处理
4. **WiFi双模式支持**: AP客户端模式和P2P直连模式的完整实现
5. **分组回调接口**: 按功能分组的监听器，避免接口污染
6. **丰富的调用示例**: 从基础使用到复杂场景的完整示例（使用Builder模式）
7. **健壮的错误处理**: 统一的异常处理和状态管理机制
8. **详细的迁移指南**: 帮助开发者平滑迁移到新架构

通过这个重构方案，RiderService SDK将具有：
- **配置集中化**: Builder模式让配置更直观、类型安全
- **更清晰的架构层次**: 职责分离明确，依赖关系清晰
- **更好的可维护性和可扩展性**: 模块化设计，易于扩展
- **更强的类型安全性**: 编译时检查，减少运行时错误
- **更优的性能表现**: 配置驱动的优化策略
- **更友好的开发体验**: 链式调用，配置意图清晰

重构后的SDK不仅解决了现有的架构问题，还通过Builder模式提供了现代化的SDK使用体验，为未来的功能扩展奠定了坚实的基础。