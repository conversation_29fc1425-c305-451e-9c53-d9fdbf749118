package com.link.riderservice.feature.display.config

/**
 * Autolink 配置类
 * @property phoneName 手机名称
 * @property version app 版本号
 * @property appName app 名称
 * @property remoteHost 远程地址
 * @property screenHeight 屏幕高度
 * @property screenWidth 屏幕宽度
 * @property isLandscape 是否横屏
 * @property rotation   屏幕旋转角度
 * @property videoCfg 视频配置
 * @property bluetoothConfig  蓝牙配置
 * @see VideoConfig
 * @see BluetoothConfig
 */
data class AlConfig(
    var phoneName: String,
    var version: String,
    var appName: String,
    var remoteHost: String? = null,
    var screenWidth: Int = 0,
    var screenHeight: Int = 0,
    var isLandscape: Int = 0,
    var rotation: Int = 0,
    var videoCfg: VideoConfig? = null,
    var bluetoothConfig: BluetoothConfig = BluetoothConfig()
)