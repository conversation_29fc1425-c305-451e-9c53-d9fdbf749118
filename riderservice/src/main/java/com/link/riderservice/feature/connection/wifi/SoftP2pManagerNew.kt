package com.link.riderservice.feature.connection.wifi

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.NetworkInfo
import android.net.wifi.WifiManager
import android.net.wifi.p2p.WifiP2pConfig
import android.net.wifi.p2p.WifiP2pDevice
import android.net.wifi.p2p.WifiP2pDeviceList
import android.net.wifi.p2p.WifiP2pManager
import android.net.wifi.p2p.WifiP2pManager.ActionListener
import android.net.wifi.p2p.WifiP2pManager.ChannelListener
import com.link.riderservice.api.RiderService
import com.link.riderservice.core.extensions.coroutines.mainScope
import com.link.riderservice.core.utils.countDownByFlow
import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.logging.logE
import com.link.riderservice.core.utils.logging.logW
import com.link.riderservice.core.utils.system.TimeUtils
import kotlinx.coroutines.Job
import java.util.concurrent.atomic.AtomicBoolean

internal class SoftP2pManagerNew(
    val listener: SoftIP2pListener
) : ChannelListener,
    WifiP2pManager.PeerListListener {
    private val context = RiderService.instance.getApplication().applicationContext
    private val manager: WifiP2pManager? by lazy(LazyThreadSafetyMode.NONE) {
        context.getSystemService(Context.WIFI_P2P_SERVICE) as WifiP2pManager?
    }

    private val wifiManager: WifiManager? by lazy(LazyThreadSafetyMode.NONE) {
        context.getSystemService(Context.WIFI_SERVICE) as WifiManager?
    }
    private var wifiP2pChannel: WifiP2pManager.Channel? =
        manager?.initialize(context, context.mainLooper, null)
    private var peerAddress: String = ""
    private var peerPort: Int = 0
    private var isConnecting = AtomicBoolean(false)
    private var retryChannel = false
    private var isWifiExist = false
    private var searchCountdownJob: Job? = null
    private var connectCountdownJob: Job? = null
    private var autolinkStation = AutolinkStationState.WIFI_START
    private var isScanning = false
    private var isWifiConnected = false

    /** wifi连接重试次数*/
    private var wifiConnectionRetryCount = 0

    // 用于监听 WLAN 直连状态的广播
    private val broadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        @SuppressLint("MissingPermission")
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                    when (intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, 0)) {
                        WifiManager.WIFI_STATE_DISABLED -> {
                            isConnecting.set(false)
                            listener.onWifiState(false)
                            autolinkStation = AutolinkStationState.WIFI_DISABLE
                        }

                        WifiManager.WIFI_STATE_ENABLING -> {
                            if (autolinkStation == AutolinkStationState.WIFI_DISABLE) {
                                listener.requestWifiInfo()
                            }
                        }

                        WifiManager.WIFI_STATE_ENABLED -> {
                            listener.onWifiState(true)
                        }
                    }
                }

                WifiP2pManager.WIFI_P2P_DISCOVERY_CHANGED_ACTION -> {
                    val discoveryState = intent.getIntExtra(
                        WifiP2pManager.EXTRA_DISCOVERY_STATE,
                        WifiP2pManager.WIFI_P2P_DISCOVERY_STOPPED
                    )
                    isScanning = discoveryState == WifiP2pManager.WIFI_P2P_DISCOVERY_STARTED
                    if(isScanning){
                        logD(TAG, "P2P discovery started. Timestamp: ${TimeUtils.getCurrentTimeStr()}")
                    }else{
                        logD(TAG, "P2P discovery stopped. Timestamp: ${TimeUtils.getCurrentTimeStr()}")
                    }
                    discoverPeers()
                }

                WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION -> {
                    logD(TAG, "P2P state changed. Timestamp: ${TimeUtils.getCurrentTimeStr()}")
                    if (intent.getIntExtra(
                            WifiP2pManager.EXTRA_WIFI_STATE,
                            WifiP2pManager.WIFI_P2P_STATE_DISABLED
                        ) == WifiP2pManager.WIFI_P2P_STATE_ENABLED
                    ) {
                        discoverPeers()
                    }
                }

                WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION -> {
                    manager?.requestPeers(wifiP2pChannel, this@SoftP2pManagerNew)
                }

                WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION -> {
                    val networkInfo =
                        intent.getParcelableExtra<NetworkInfo>(WifiP2pManager.EXTRA_NETWORK_INFO)
                    logD(TAG, "P2P connection changed: ${networkInfo?.isConnected}")

                    if (networkInfo?.isConnected == true) {
                        logD(TAG, "P2P connected. Timestamp: ${TimeUtils.getCurrentTimeStr()}")
                        // 避免重复处理已经成功的连接
                        if (autolinkStation != AutolinkStationState.CONNECT_SUCCESS) {
                            if (autolinkStationChange(AutolinkStationState.CONNECT_SUCCESS)) {
                                wifiConnectionRetryCount = 0
                                isWifiConnected = true
                                connectCountdownJob?.cancel()
                                listener.onWifiConnectSuccess()
                                stopSearch()
                            }
                        } else {
                            logD(TAG, "P2P connection already in CONNECT_SUCCESS state, skipping duplicate handling")
                        }
                    } else {
                        if (isWifiConnected) {
                            logD(TAG, "P2P disconnected while previously connected")
                            isWifiConnected = false
                            isConnecting.set(false)
                            // 仅在当前是连接状态时才尝试转换到断开状态
                            if (autolinkStation == AutolinkStationState.CONNECT_SUCCESS) {
                                autolinkStationChange(AutolinkStationState.NO_CONNECT)
                            }
                        }
                        listener.onWifiDisconnect()
                    }
                }

                WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION -> {
                    val device =
                        intent.getParcelableExtra<WifiP2pDevice>(WifiP2pManager.EXTRA_WIFI_P2P_DEVICE)
                    logD(TAG, "P2P device status: ${device?.status}")
                    if (device?.status == WifiP2pDevice.CONNECTED && !isConnecting.get()) {
                        logD(TAG, "P2P device connected: ${device.deviceName}")
                        disconnect()
                        listener.requestWifiInfo()
                    }
                }
            }
        }
    }

    /**
     * 初始化 WLAN 直连
     */
    @SuppressLint("MissingPermission")
    internal fun start(address: String, port: Int) {
        logD(TAG, "start() called. isScanning = $isScanning")
        if (isScanning) {
            stop()
        }
        logD(TAG, "p2p module start")
        isConnecting.set(false)
        peerAddress = address
        peerPort = port
        autolinkStationChange(AutolinkStationState.WIFI_START)
        val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
        if (!wifiManager.isWifiEnabled) {
            listener.onWifiState(false)
        }
        //判断是否已经连接
        manager?.requestConnectionInfo(wifiP2pChannel) {
            if (it.groupFormed) {
                if (autolinkStationChange(AutolinkStationState.ALREADY_CONNECTED)) {
                    isWifiConnected = true
                    isConnecting.set(true)
                }
            } else {
                if (autolinkStationChange(AutolinkStationState.NO_CONNECT)) {
                    wifiConnectionRetryCount = 0
                    searchExist()
                }
            }
        }
        //开启搜索
        discoverPeers()
    }

    fun isWifiEnabled(): Boolean {
        return wifiManager?.isWifiEnabled == true
    }

    internal fun stop() {
        logD(TAG, "p2p module stop")
        //停止搜索
        stopSearch()
        //断开连接
        disconnect()

        connectCountdownJob?.cancel()
        connectCountdownJob = null
        isWifiConnected = false
    }

    private fun initP2p(): Boolean {
        if (!context.packageManager.hasSystemFeature(PackageManager.FEATURE_WIFI_DIRECT)) {
            logE(TAG, "Wi-Fi Direct is not supported by this device.")
            return false
        }
        if (wifiManager == null) {
            logE(TAG, "Cannot get Wi-Fi system service.")
            return false
        }
        if (wifiManager?.isP2pSupported == false) {
            logE(TAG, "Wi-Fi Direct is not supported by the hardware or Wi-Fi is off.")
            return false
        }

        if (manager == null) {
            logE(TAG, "Cannot get Wi-Fi Direct system service.")
            return false
        }
        return true
    }

    @SuppressLint("MissingPermission")
    @Synchronized
    private fun connect(wifiP2pDevice: WifiP2pDevice) {
        wifiConnectionRetryCount++
        isWifiExist = true
        if (wifiP2pDevice.status == WifiP2pDevice.AVAILABLE && !isConnecting.get()) {
            logD(TAG, "connect wifi ${wifiP2pDevice.deviceName} ${wifiP2pDevice.deviceAddress}")
            isConnecting.set(true)
            val config = WifiP2pConfig()
            config.deviceAddress = wifiP2pDevice.deviceAddress
            logD(TAG, "start wifi connect::${TimeUtils.getCurrentTimeStr()}}")
            manager?.connect(wifiP2pChannel, config, object : ActionListener {
                override fun onSuccess() {
                    logD(TAG, "Initiated P2P connection to ${config.deviceAddress}. Timestamp: ${TimeUtils.getCurrentTimeStr()}")
                    listener.onP2pConnectSuccess()
                    startConnectCountDown()
                }

                override fun onFailure(reason: Int) {
                    logD(TAG, "Connect failed. Reason: $reason. Timestamp: ${TimeUtils.getCurrentTimeStr()}")
                    isConnecting.set(false)
                }
            })
        }
    }


    private fun cancelConnect() {
        wifiP2pChannel?.run {
            manager?.cancelConnect(this, object : ActionListener {
                override fun onSuccess() {
                    logD(TAG, "cancelConnect")
                }

                override fun onFailure(reasonCode: Int) {
                    logE(TAG, "cancelConnect fail reason:$reasonCode")
                }

            })
        }
        listener.onCancelConnect()
    }

    private fun registerReceiver() {
        logD(TAG, "register wifi receiver")
        context.registerReceiver(broadcastReceiver, IntentFilter().apply {
            addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_DISCOVERY_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION)
        })
    }

    private fun unregisterReceiver() {
        logD(TAG, "unregister wifi receiver")
        try {
            context.unregisterReceiver(broadcastReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @SuppressLint("MissingPermission")
    private fun discoverPeers() {
        if (isWifiConnected) {
            return
        }
        if (isScanning) {
            manager?.requestPeers(wifiP2pChannel, this@SoftP2pManagerNew)
        } else {
            wifiP2pChannel?.let {
                manager?.discoverPeers(it, object : ActionListener {
                    override fun onSuccess() {
                        logD(TAG, "discoverPeers Success")
                    }

                    override fun onFailure(reasonCode: Int) {
                        logD(TAG, "discoverPeers failed: $reasonCode")
                    }
                })
            }
        }
    }

    @SuppressLint("MissingPermission")
    private fun stopSearch() {
        if (!isScanning) {
            return
        }
        manager?.stopPeerDiscovery(wifiP2pChannel, object : ActionListener {
            override fun onSuccess() {
                logD(TAG, "stopPeerDiscovery Success. Timestamp: ${TimeUtils.getCurrentTimeStr()}")
            }

            override fun onFailure(reasonCode: Int) {
                logE(TAG, "stopPeerDiscovery failed: $reasonCode")
            }
        })
    }

    private fun disconnect() {
        logD(TAG, "disconnect() called. Timestamp: ${TimeUtils.getCurrentTimeStr()}")
        
        // 设置断开连接标志
        isWifiExist = false
        isConnecting.set(false)
        
        // 只有在确实连接的情况下才执行断开操作
        if (isWifiConnected && autolinkStation == AutolinkStationState.CONNECT_SUCCESS) {
            wifiP2pChannel?.run {
                manager?.removeGroup(this, object : ActionListener {
                    override fun onSuccess() {
                        logD(TAG, "disconnect success")
                        // 更新状态
                        isWifiConnected = false
                        if (autolinkStationChange(AutolinkStationState.NO_CONNECT)) {
                            wifiConnectionRetryCount = 0
                            searchExist()
                        }
                    }

                    override fun onFailure(reasonCode: Int) {
                        logD(TAG, "disconnect failed. Reason :$reasonCode")
                        // 即使断开失败，也要重置状态
                        isWifiConnected = false
                        autolinkStationChange(AutolinkStationState.NO_CONNECT)
                    }
                })
            }
        } else {
            // 如果没有连接或状态不匹配，直接重置
            isWifiConnected = false
            logD(TAG, "No active P2P connection to disconnect, resetting state")
        }
    }


    override fun onChannelDisconnected() {
        logW(TAG, "Channel lost")
        if (manager != null && !retryChannel) {
            retryChannel = true
            wifiP2pChannel = manager?.initialize(context, context.mainLooper, this)
        }
    }

    override fun onPeersAvailable(peers: WifiP2pDeviceList) {
        val foundDevices = peers.deviceList
        foundDevices?.run {
            find { it.isGroupOwner && it.status == WifiP2pDevice.CONNECTED }
                ?.let {
                    if (it.deviceAddress.lowercase() != peerAddress.lowercase()) {
                        logD(TAG, "Found an unexpected P2P group owner: ${it.deviceAddress}, current target: $peerAddress")
                        if (autolinkStationChange(AutolinkStationState.P2P_DEVICE_ERROR)) {
                            disconnect() // Disconnect from the unexpected group
                        }
                    }
                    // If it *is* our peerAddress and already connected as GO, this will be handled by the block below.
                }

            find { it.deviceAddress.lowercase() == peerAddress.lowercase() }?.let { currentPeerDevice ->
                logD(TAG, "Target peer ${currentPeerDevice.deviceName} found. Status: ${currentPeerDevice.status}, isConnecting: ${isConnecting.get()}, current autolinkStation: $autolinkStation")
                if (isConnecting.get()) {
                    // We are in the process of connecting to this peer.
                    // Check if the peer's status now shows it's fully connected as Group Owner.
                    if (currentPeerDevice.isGroupOwner && currentPeerDevice.status == WifiP2pDevice.CONNECTED) {
                        logD(TAG, "Target peer ${currentPeerDevice.deviceName} became connected as GO while 'isConnecting' was true.")
                        // 避免重复处理已经成功的连接
                        if (autolinkStation != AutolinkStationState.CONNECT_SUCCESS) {
                            // Attempt to transition to CONNECT_SUCCESS, mirroring WIFI_P2P_CONNECTION_CHANGED_ACTION handling
                            if (autolinkStationChange(AutolinkStationState.CONNECT_SUCCESS)) {
                                logD(TAG, "Transitioned to CONNECT_SUCCESS based on peer status in onPeersAvailable.")
                                wifiConnectionRetryCount = 0 // Reset retry count on success
                                isWifiConnected = true
                                connectCountdownJob?.cancel() // Cancel pending timeout
                                listener.onWifiConnectSuccess() // Notify listener
                                stopSearch() // Stop further discovery
                            } else {
                                logW(TAG, "Target peer is CONNECTED as GO, but state transition to CONNECT_SUCCESS failed from $autolinkStation.")
                            }
                        } else {
                            logD(TAG, "Target peer is CONNECTED as GO, but already in CONNECT_SUCCESS state. Ensuring consistency.")
                            // If already in CONNECT_SUCCESS (due to a race with broadcast), ensure flags are set.
                            isWifiConnected = true // Ensure consistency
                        }
                    } else {
                        // Peer is available (e.g., status AVAILABLE, INVITED, FAILED), but not yet fully connected as GO.
                        // We are already connecting, so just log and wait for WIFI_P2P_CONNECTION_CHANGED_ACTION or connect timeout.
                        logD(TAG, "Target peer ${currentPeerDevice.deviceName} status is ${currentPeerDevice.status} while 'isConnecting' is true. Awaiting definitive connection result via broadcast.")
                    }
                } else {
                    // Standard logic: not currently connecting to this peer, so initiate connection if found and available.
                    if (currentPeerDevice.status == WifiP2pDevice.AVAILABLE) {
                        if (autolinkStationChange(AutolinkStationState.SEARCH_SUCCESS)) {
                            connect(currentPeerDevice)
                        }
                    } else {
                        logD(TAG, "Target peer ${currentPeerDevice.deviceName} found with status ${currentPeerDevice.status}, not AVAILABLE. Cannot initiate connect at this moment.")
                    }
                }
            }
        }
    }

    private fun startConnectCountDown() {
        connectCountdownJob = countDownByFlow(10, 1000, mainScope,
            onTick = {
            }, onFinish = {
                logD(TAG, "startConnectCountDown")
                if (!isWifiConnected) {
                    cancelConnect()
                }
            })
    }

    private fun searchExist() {
        if (autolinkStationChange(AutolinkStationState.SEARCH_WIFI)) {
            searchCountdownJob = countDownByFlow(5, 1000, mainScope,
                onTick = {
                    logD(TAG, "search result:$isWifiExist")
                    if (isWifiExist) {
                        searchCountdownJob?.cancel()
                    }
                }, onFinish = {
                    //扫描10秒后没有找到wifi，请求重置平台端wifi
                    if (autolinkStationChange(AutolinkStationState.SEARCH_FAILED)) {
                        logD(TAG, "search finish result:$isWifiExist")
                        if (!isWifiExist) {
                            listener.requestWifiInfo()
                        }
                    }
                })
        }
    }

    private fun autolinkStationChange(newState: AutolinkStationState): Boolean {
        if (newState == autolinkStation) {
            logD(TAG, "AutolinkStation: already in state $newState, no change required.")
            return true // Indicate success as no change was needed or it's already correct
        }
        val previousState = autolinkStation
        val changed = when (previousState) {
            AutolinkStationState.WIFI_START -> newState == AutolinkStationState.NO_CONNECT || newState == AutolinkStationState.ALREADY_CONNECTED
            AutolinkStationState.NO_CONNECT -> newState == AutolinkStationState.SEARCH_WIFI || newState == AutolinkStationState.WIFI_START
            AutolinkStationState.ALREADY_CONNECTED -> newState == AutolinkStationState.WIFI_START || newState == AutolinkStationState.AUTOLINK_RESTART || newState == AutolinkStationState.P2P_DEVICE_ERROR || newState == AutolinkStationState.CONNECT_SUCCESS
            AutolinkStationState.SEARCH_WIFI -> newState == AutolinkStationState.SEARCH_FAILED || newState == AutolinkStationState.SEARCH_SUCCESS
            AutolinkStationState.SEARCH_FAILED -> newState == AutolinkStationState.WIFI_START
            AutolinkStationState.SEARCH_SUCCESS -> newState == AutolinkStationState.CONNECT_SUCCESS || newState == AutolinkStationState.CONNECT_FAILED || newState == AutolinkStationState.WIFI_START
            AutolinkStationState.CONNECT_FAILED -> newState == AutolinkStationState.CONNECT_SUCCESS || newState == AutolinkStationState.RECONNECT_FAILED
            AutolinkStationState.CONNECT_SUCCESS -> newState == AutolinkStationState.WIFI_START || newState == AutolinkStationState.NO_CONNECT
            AutolinkStationState.RECONNECT_FAILED -> newState == AutolinkStationState.WIFI_START
            AutolinkStationState.AUTOLINK_RESTART -> newState == AutolinkStationState.WIFI_START
            AutolinkStationState.P2P_DEVICE_ERROR -> newState == AutolinkStationState.NO_CONNECT
            AutolinkStationState.WIFI_DISABLE -> newState == AutolinkStationState.WIFI_START
        }

        if (changed) {
            autolinkStation = newState
            logD(TAG, "AutolinkStation: $previousState -> $newState")
            
            // 在状态转换后同步连接状态
            syncConnectionState()
            return true
        }
        logE(TAG, "AutolinkStation: $previousState -> $newState transition failed.")
        return false
    }

    init {
        if (wifiP2pChannel == null) {
            logE(TAG, "Cannot initialize Wi-Fi Direct.")
        }
        if (!initP2p()) {
            logE(TAG, "initP2p failed")
        }
        registerReceiver()
    }

    private enum class AutolinkStationState {
        WIFI_START,                // 进入app时，未获取到其他状态
        NO_CONNECT,                // 进入app时，wifi p2p未连接
        ALREADY_CONNECTED,         // 进入app时，wifi p2p已经连接
        SEARCH_WIFI,               // 搜索wifi，持续搜索10s
        SEARCH_FAILED,             // 搜索wifi重试失败，向平台请求重置
        SEARCH_SUCCESS,            // wifi搜索成功
        CONNECT_SUCCESS,           // wifi连接成功
        CONNECT_FAILED,            // wifi连接失败，重试3次
        RECONNECT_FAILED,          // wifi连接重试失败，向平台请求重置
        AUTOLINK_RESTART,          // wifi已经连接，进入Autolink重启
        P2P_DEVICE_ERROR,          // wifi p2p对等设备错误，移除设备
        WIFI_DISABLE               // wifi关闭
    }

    /**
     * 重置P2P连接状态
     */
    fun resetConnectionState() {
        logD(TAG, "Resetting P2P connection state")
        
        // 停止搜索和连接
        stop()
        
        // 重置状态变量
        isConnecting.set(false)
        isWifiConnected = false
        isWifiExist = false
        wifiConnectionRetryCount = 0
        isScanning = false
        retryChannel = false
        
        // 重置状态机
        autolinkStation = AutolinkStationState.WIFI_START
        
        // 取消所有倒计时
        searchCountdownJob?.cancel()
        connectCountdownJob?.cancel()
        searchCountdownJob = null
        connectCountdownJob = null
        
        logD(TAG, "P2P connection state reset completed")
    }

    /**
     * 强制同步状态 - 用于解决状态不一致问题
     */
    private fun syncConnectionState() {
        logD(TAG, "Syncing connection state - autolinkStation: $autolinkStation, isWifiConnected: $isWifiConnected, isConnecting: ${isConnecting.get()}")
        
        // 如果状态不一致，进行修正
        when {
            autolinkStation == AutolinkStationState.CONNECT_SUCCESS && !isWifiConnected -> {
                logW(TAG, "State inconsistency detected: CONNECT_SUCCESS but isWifiConnected=false. Correcting...")
                isWifiConnected = true
            }
            autolinkStation != AutolinkStationState.CONNECT_SUCCESS && isWifiConnected -> {
                logW(TAG, "State inconsistency detected: isWifiConnected=true but not in CONNECT_SUCCESS. Correcting...")
                isWifiConnected = false
            }
        }
    }

    /**
     * 获取P2P连接信息
     */
    fun getConnectionInfo(): String {
        return buildString {
            append("P2P Manager Status:\n")
            append("- Autolink Station: $autolinkStation\n")
            append("- Is Connecting: ${isConnecting.get()}\n")
            append("- Is WiFi Connected: $isWifiConnected\n")
            append("- Is Scanning: $isScanning\n")
            append("- WiFi Exists: $isWifiExist\n")
            append("- Retry Count: $wifiConnectionRetryCount\n")
            append("- Target Address: $peerAddress\n")
            append("- Target Port: $peerPort\n")
            append("- Retry Channel: $retryChannel")
        }
    }

    companion object {
        private const val TAG = "SoftP2pManagerNew"
    }
}
