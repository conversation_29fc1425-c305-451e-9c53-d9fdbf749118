package com.link.riderservice.feature.analytics

/**
 * 连接指标数据类
 * 用于记录和分析连接相关的统计数据
 */
data class ConnectionMetrics(
    val sessionId: String,
    val connectionType: ConnectionType,
    val deviceId: String,
    val startTime: Long,
    val endTime: Long? = null,
    val status: ConnectionStatus = ConnectionStatus.CONNECTING,
    val retryCount: Int = 0,
    val connectionInfo: ConnectionInfo? = null,
    val error: ConnectionError? = null
) {
    /**
     * 连接持续时间（毫秒）
     */
    val duration: Long
        get() = (endTime ?: System.currentTimeMillis()) - startTime
    
    /**
     * 连接是否已完成（成功或失败）
     */
    val isCompleted: Boolean
        get() = status != ConnectionStatus.CONNECTING
    
    /**
     * 连接是否成功
     */
    val isSuccessful: Boolean
        get() = status == ConnectionStatus.SUCCESS
}

/**
 * 连接类型枚举
 */
enum class ConnectionType {
    /** 蓝牙低功耗连接 */
    BLE,
    /** WiFi AP模式连接 */
    WIFI_AP,
    /** WiFi P2P直连 */
    WIFI_P2P,
    /** TCP连接 */
    TCP,
    /** 未知类型 */
    UNKNOWN
}

/**
 * 连接状态枚举
 */
enum class ConnectionStatus {
    /** 连接中 */
    CONNECTING,
    /** 连接成功 */
    SUCCESS,
    /** 连接失败 */
    FAILED,
    /** 连接超时 */
    TIMEOUT,
    /** 连接被取消 */
    CANCELLED
}

/**
 * 连接信息数据类
 */
data class ConnectionInfo(
    /** IP地址 */
    val ipAddress: String? = null,
    /** WiFi SSID */
    val ssid: String? = null,
    /** 信号强度 */
    val signalStrength: Int? = null,
    /** 带宽（字节/秒） */
    val bandwidth: Long? = null,
    /** 额外信息 */
    val extras: Map<String, Any> = emptyMap()
)

/**
 * 连接错误数据类
 */
data class ConnectionError(
    /** 错误代码 */
    val code: Int,
    /** 错误消息 */
    val message: String,
    /** 错误原因 */
    val cause: Throwable? = null,
    /** 错误类型 */
    val type: ErrorType = ErrorType.UNKNOWN
) {
    override fun toString(): String {
        return "ConnectionError(code=$code, message='$message', type=$type)"
    }
}

/**
 * 错误类型枚举
 */
enum class ErrorType {
    /** 网络不可用 */
    NETWORK_UNAVAILABLE,
    /** 权限错误 */
    PERMISSION_ERROR,
    /** WiFi未开启 */
    WIFI_DISABLED,
    /** 认证失败 */
    AUTHENTICATION_FAILED,
    /** TCP连接失败 */
    TCP_CONNECTION_FAILED,
    /** 设备未找到 */
    DEVICE_NOT_FOUND,
    /** 连接超时 */
    CONNECTION_TIMEOUT,
    /** 未知错误 */
    UNKNOWN
}
