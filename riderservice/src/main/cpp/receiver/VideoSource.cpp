// Copyright 2014 Google Inc. All Rights Reserved.

#include "VideoSource.h"
#include "util/Timers.h"
#include <media/NdkMediaCodec.h>

int VideoSource::routeMessage(uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg) {
    int ret = STATUS_UNEXPECTED_MESSAGE;
    uint8_t *ptr = (uint8_t *) msg->raw() + sizeof(uint16_t);
    size_t len = msg->size() - sizeof(uint16_t);
    switch (type) {
        case VIDEO_MESSAGE_FOCUS_NOTIFICATION: {
            LOG("[VideoSource] <- VideoFocusNotification\n");
            VideoFocusNotification vfn;
            if (PARSE_PROTO(vfn, ptr, len)) {
                ret = handleVideoFocusNotif(vfn);
            }
            break;
        }
        case VIDEO_MESSAGE_DISPLAY_CONFIGURATION_CHANGE_REQUEST: {
            LOG("[VideoSource] <- DisplayChange\n");
            DisplayConfigurationChangeRequest displayCR;
            if (PARSE_PROTO(displayCR, ptr, len)) {
                ret = handleDisplayChange(displayCR);
            }
            break;
        }
        default: {
            ret = MediaSourceBase::routeMessage(channelId, type, msg);
            break;
        }
    }
    return ret;
}

bool VideoSource::discoverService(const Service &srv) {
    bool ret = false;
    if (srv.has_media_sink_service()) {
        const MediaSinkService *msrv = &srv.media_sink_service();
        if (msrv->available_type() == MEDIA_CODEC_VIDEO_H264_BP
            && msrv->video_configs_size() > 0) {
            for (int i = 0; i < msrv->video_configs_size(); i++) {
                const VideoConfiguration *vc = &(msrv->video_configs(i));
                int32_t codec = vc->vcodec_type();
                int32_t fps = vc->frame_rate();
                int32_t w = 800, h = 480;
                if (vc->codec_resolution() == VIDEO_800x480) {
                    w = 800;
                    h = 480;
                } else if (vc->codec_resolution() == VIDEO_1280x720) {
                    w = 1280;
                    h = 720;
                } else if (vc->codec_resolution() == VIDEO_1920x1080) {
                    w = 1920;
                    h = 1080;
                }
                if (vc->has_codec_width() && vc->has_codec_height()) {
                    w = static_cast<int32_t>(vc->codec_width());
                    h = static_cast<int32_t>(vc->codec_height());
                }
                if (fps == VIDEO_FPS_30) {
                    fps = 30;
                } else if (fps == VIDEO_FPS_60) {
                    fps = 60;
                }
                ret |= mCallbacks->discoverVideoConfigCallback(codec, fps, w, h);
            }
        }
    }
    return ret;
}

void VideoSource::onChannelOpened(uint8_t channelId, uint32_t extraMessage) {
    ProtocolEndpointBase::onChannelOpened(channelId, extraMessage);
    mCallbacks->onChannelOpened();
}

int VideoSource::handleConfig(const Config &config) {
    int32_t status = config.status();
    uint32_t max_unacked = config.max_unacked();
    uint32_t prefer_size = config.configuration_indices_size();
    uint32_t *prefer = nullptr;
    if (prefer_size > 0) {
        prefer = new uint32_t[prefer_size];
        for (uint32_t i = 0; i < prefer_size; i++) {
            prefer[i] = config.configuration_indices(i);
        }
    }
    if (config.has_video_transport_mode()) {
        mVideoTransportMode = config.video_transport_mode();
    }
    if (mVideoTransportMode == Config_VideoTransportMode_VIDEO_TRANSPORT_MODE_RTP_OVER_UDP) {
        createRtpSession(mRemoteHost);
    }
    return mCallbacks->configCallback(status, max_unacked, prefer, prefer_size);
}

int VideoSource::handleAck(const Ack &ack) {
    uint32_t session_id = ack.session_id();
    uint32_t ack_num = ack.ack();
    if (mCallbacks != nullptr) {
        return mCallbacks->ackCallback(session_id, ack_num);
    }
    return 0;
}

int VideoSource::handleVideoFocusNotif(const VideoFocusNotification &vfn) {
    uint32_t focus = vfn.focus();
    bool unsolicited = vfn.unsolicited();
    return mCallbacks->videoFocusNotifCallback(focus, unsolicited);
}

int VideoSource::handleDisplayChange(const DisplayConfigurationChangeRequest &displayCR) {
    int32_t width = displayCR.width();
    int32_t height = displayCR.height();
    int32_t density = displayCR.density();
    bool isLandscape = false;
    LOG("orientation %d",displayCR.orientation());
    if (displayCR.orientation() == LANDSCAPE) {
        isLandscape = true;
    }
    return mCallbacks->displayChangeCallback(width, height, isLandscape, density);
}

void VideoSource::sendVideoFocusRequest(
        int32_t channelId, uint32_t mode, uint32_t reason) {
    LOG("[VideoSource] -> VideoFocusRequest\n");
    VideoFocusRequestNotification vfn;
    vfn.set_disp_channel_id(channelId);
    vfn.set_mode((VideoFocusMode) mode);
    vfn.set_reason((VideoFocusReason) reason);
    IoBuffer buf;
    MessageRouter::marshallProto(VIDEO_MESSAGE_FOCUS_REQUEST, vfn, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void VideoSource::sendDisplayAreaChangeResponse() {
    LOG("[VideoSource] -> DisplayAreaChangeResponse\n");
    DisplayConfigurationChangeResponse displayCR;
    IoBuffer buf;
    MessageRouter::marshallProto(VIDEO_MESSAGE_DISPLAY_CONFIGURATION_CHANGE_RESPONSE, displayCR,
                                 &buf);
    queueOutgoing(buf.raw(), buf.size());

}


int VideoSource::handleStartResponse(StartResponse &response) {
    return mCallbacks->startResponseCallback(response.ok());
}

int VideoSource::handleStopResponse(StopResponse &response) {
    return mCallbacks->stopResponseCallback();
}

void VideoSource::sendVideoOrientation(bool isLandscape) {

}

int VideoSource::createRtpSession(string &remoteHost) {
    if (!remoteHost.empty()) {
        LOGD("mRemoteHost:%s\n", remoteHost.c_str());
        mNetSession = new NetworkSession();
        mNetSession->start();
        mRtpSender = new RTPSender(mNetSession);
        status_t err = mRtpSender->initAsync(remoteHost.c_str(), kUDPPort, RTPBase::TRANSPORT_UDP,
                                             -1, RTPBase::TRANSPORT_UDP, &mLocalRTPPort);
        if (err != OK) {
            mLocalRTPPort = -1;
            mRtpSender = nullptr;
            return err;
        }
    }
    return OK;
}

void VideoSource::sendData(uint64_t timeStamp, void *data, size_t len, int32_t flags) {
    switch (mVideoTransportMode) {
        case Config_VideoTransportMode_VIDEO_TRANSPORT_MODE_RTP_OVER_UDP: {
            if (mRtpSender != nullptr) {
                mRtpSender->queueBuffer(data, len, 96, RTPBase::PACKETIZATION_H264);
            }
            break;
        }
        default: {
            if (flags == AMEDIACODEC_BUFFER_FLAG_CODEC_CONFIG ||
                flags == BUFFER_FLAG_CODEC_CONFIG) {
                MediaSourceBase::sendCodecConfig(data, len);
            } else {
                MediaSourceBase::sendData(timeStamp, data, len, true);
            }
            break;
        }
    }
}






