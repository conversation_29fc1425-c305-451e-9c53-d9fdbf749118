// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_PROTOCOL_ENDPOINT_BASE_H
#define AUTOLINK_PROTOCOL_PROTOCOL_ENDPOINT_BASE_H

#include "util/common.h"
#include "MessageRouter.h"

/**
 * @internal
 * This class forms the base for all 'Services' advertised by the head unit.
 * All standard services and vendor extension services must inherit from this
 * base class. When a message is received on a channel that has been opened
 * against this service, a call to routeMessage() is made with the channelId,
 * type and a buffer with the raw message.
 *
 * While channel ids are provided in routeMessage, most services will not have
 * to concern themselves with a channel id.
 */
class ProtocolEndpointBase {
public:
    ProtocolEndpointBase(uint8_t id, MessageRouter *router, bool isPassthrough) :
            mConnected(false), mChannelId(0), mR<PERSON>er(router), mLocalServiceId(id), mPriority(0),
            mRemoteServiceId(SENTINEL_SERVICE_ID), mIsPassthrough(isPassthrough),
            mExtraMessage(0) {}

    virtual ~ProtocolEndpointBase() = default;

    inline uint8_t id() const { return mLocalServiceId; }

    inline int8_t priority() const { return mPriority; }

    inline bool isPassthrough() const { return mIsPassthrough; }

    /**
     * Gets invoked by the MessageRouter every time a channel against this endpoint is closed.
     * @param channelId The channel id that was closed.
     * @return true if the close was successful, false on any internal error.
     */
    virtual bool onChannelClosed(uint8_t channelId);

    /**
     * Gets invoked by the MessageRouter to check whether a channel may be opened or not.
     * @param channelId The channel id that wants to be opened.
     * @return true if the channel may be opened, false otherwise.
     */
    virtual bool mayOpenChannel(uint8_t channelId);

    /**
     * Gets invoked by the MessageRouter every time a channel against this endpoint is opened.
     * @param channelId The channel id that was opened.
     */
    virtual void onChannelOpened(uint8_t channelId, uint32_t extraMessage);

    /**
     * Gets called by the MessageRouter when it receives a message on a channel that points to
     * this ProtocolEndpoint if it isn't configured as a passthrough endpoint.
     * @param channelId The channel id on which the message arrived.
     * @param type The value of the 2 byte type field in host endian format.
     * @param msg An IoBuffer that contains the actual bytes of the message.
     */
    virtual int routeMessage(uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg) {
        return STATUS_INVALID_SERVICE;
    }

    /**
     * Gets called by the MessageRouter when it receives a message on a channel that points to
     * this ProtocolEndpoint if it is configured as a passthrough endpoint.
     * @param channelId The id of the channel that this message arrived on.
     * @param msg A pointer to the actual message.
     * @return Must return zero on success or a known status code on failure.
     */
    virtual int handleRawMessage(uint8_t channelId, const shared_ptr<IoBuffer> &msg) {
        return STATUS_INVALID_SERVICE;
    }

    /**
     * This callback is invoked by the MessageRouter when received ServicedDiscoveryResponse,
     * Each ProtocolEndpoint is responsible for find its own service from ServiceDiscoveryResponse.
     * @param sdr The ServiceDiscoveryResponse object that received from HU.
     * @param true if find the service from ServiceDiscoveryResponse.
     */
    virtual bool discoverService(const Service &srv);

    virtual void addDiscoveryInfo(ServiceDiscoveryResponse *sdr);

    virtual bool isExtraMessageExist(MediaMessageExistanceSetup message);

    /**
     * Call this to forcefully close the channel from the head unit.
     * Only passthrough channels can be closed this way.
     * @return true if a close channel message was sent.
     */
    bool closeChannel() {
        if (mConnected && mIsPassthrough) {
            return mRouter->closeChannel(mChannelId);
        }
        return false;
    }

    bool forceCloseChannel() {
        if (mConnected) {
            return mRouter->forceCloseChannel(mChannelId);
        }
        return false;
    }

protected:
    inline int queueOutgoing(void *buf, size_t len) {
        if (!mConnected) {
            return false;
        }
        return mRouter->queueOutgoing(mChannelId, buf, len);
    }

    inline int queueOutgoingUnencrypted(void *buf, size_t len) {
        if (!mConnected) {
            return false;
        }
        return mRouter->queueOutgoingUnencrypted(mChannelId, buf, len);
    }

    bool mConnected;
    uint8_t mChannelId;
    uint32_t mExtraMessage;
    MessageRouter *mRouter;

private:
    int8_t mLocalServiceId;
    int8_t mRemoteServiceId;
    int8_t mPriority;

    bool mIsPassthrough;
};

#endif // AUTOLINK_PROTOCOL_PROTOCOL_ENDPOINT_BASE_H
