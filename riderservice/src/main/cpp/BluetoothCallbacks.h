// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef ANDROID_AUTO_PROJECTION_PROTOCOL_BLUETOOTH_CALLBACKS_H
#define ANDROID_AUTO_PROJECTION_PROTOCOL_BLUETOOTH_CALLBACKS_H

#include "NativeCallbackWrapper.h"
#include "util/common.h"
#include "IBluetoothCallbacks.h"

class BluetoothCallbacks : public NativeCallbackWrapper, public IBluetoothCallbacks {
public:
    BluetoothCallbacks(JNIEnv *env, jobject jthis) : NativeCallbackWrapper(env, jthis) {
        mChannelOpenCallbackId = env->GetMethodID(
                mThisClass, "onChannelOpened", "()I");
        mDiscoverBtCallbackId = env->GetMethodID(
                mThisClass, "discoverBluetoothService", "(Ljava/lang/String;I)Z");
        mPairingResponseCallbackId = env->GetMethodID(
                mThisClass, "onPairingResponse", "(IZ)V");
        mAuthenticationCallbackId = env->GetMethodID(
                mThisClass, "onAuthenticationData", "(Ljava/lang/String;)V");
        mPhoneBluetoothStatusInquireId = env->GetMethodID(
                mThisClass, "onPhoneBluetoothStatusInquire", "()V");
    }

    int onChannelOpened() override;

    bool discoverBluetoothService(string carAddress, uint32_t methodsBitmap) override;

    void onPairingResponse(int32_t status, bool alreadyPaired) override;

    void onAuthenticationData(string authData) override;

    void onPhoneBluetoothStatusInquire() override;

private:
    jmethodID mChannelOpenCallbackId;
    jmethodID mDiscoverBtCallbackId;
    jmethodID mPairingResponseCallbackId;
    jmethodID mAuthenticationCallbackId;
    jmethodID mPhoneBluetoothStatusInquireId;
};

#endif // ANDROID_AUTO_PROJECTION_PROTOCOL_BLUETOOTH_CALLBACKS_H
