package com.link.riderservice.feature.connection.ble

import android.bluetooth.BluetoothDevice
import com.link.riderservice.core.utils.ble.BeaconParser
import com.link.riderservice.libs.ble.scanner.ScanResult

data class BleDevice(
    val scanResult: ScanR<PERSON>ult,
    val device: BluetoothDevice = scanResult.device,
    val name: String? = scanResult.scanRecord?.deviceName,
    val rssi: Int = if (scanResult.rssi < 0) scanResult.rssi else -128,
) {
    fun update(scanResult: ScanResult): BleDevice {
        return copy(
            scanResult = scanResult,
            rssi = if (scanResult.rssi < 0) scanResult.rssi else rssi,
        )
    }

    fun completeLocalName(): String? = scanResult.scanRecord?.let {
        it.bytes?.let { bytes ->
            BeaconParser.parseBeacon(bytes)
                .find { item ->
                    item.type == 0x09
                }?.bytes?.toString(Charsets.UTF_8)
        }
    }
}
