package com.link.riderservice.data.authorization.domain.entity

import com.link.riderservice.core.utils.serialization.JSON
import com.link.riderservice.core.utils.serialization.JSONParser
import com.link.riderservice.core.utils.serialization.compareTo
import com.link.riderservice.core.utils.serialization.get

data class Activate(
    var status: Int,
    var uuid: String
)

class ActivateParser : JSONParser<Activate> {
    override fun parse(json: JSON): Activate {
        val activate = Activate(0, "")
        activate::status < json["status"]
        activate::uuid < json["uuid"]
        return activate
    }
}
