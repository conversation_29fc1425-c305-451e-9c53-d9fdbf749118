package com.link.riderservice.feature.display.protocol.project

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
object Protos {
    // VideoFocusReason
    const val UNKNOWN = 0
    const val PHONE_SCREEN_OFF = 1
    const val LAUNCH_NATIVE = 2
    const val NO_PERMISSION = 3
    const val WAIT_PERMISSION = 4
    const val NO_VALID_VIDEO_ENCODER = 5

    // VideoFocusMode
    const val VIDEO_FOCUS_PROJECTED = 1
    const val VIDEO_FOCUS_NATIVE = 2
    const val VIDEO_FOCUS_NATIVE_TRANSIENT = 3

    // MediaCodecType
    const val MEDIA_CODEC_AUDIO_PCM = 1
    const val MEDIA_CODEC_AUDIO_AAC_LC = 2
    const val MEDIA_CODEC_VIDEO_H264_BP = 3
    const val MEDIA_CODEC_AUDIO_AAC_LC_ADTS = 4
    const val MEDIA_CODEC_VIDEO_MPEG4_ES = 5

    // BluetoothPairingMethod
    const val BLUETOOTH_PAIRING_OOB = 1
    const val BLUETOOTH_PAIRING_NUMERIC_COMPARISON = 2
    const val BLUETOOTH_PAIRING_PASSKEY_ENTRY = 3
    const val BLUETOOTH_PAIRING_PIN = 4

    // Config_Status
    const val Config_Status_STATUS_WAIT = 1
    const val Config_Status_STATUS_READY = 2

    // MessageStatus
    const val STATUS_SUCCESS = 0

    enum class State(val status: Int, val desc: String) {
        START(1, "connection start"),
        STOP(2, "connection stop"),
        PROBE_START(3, "probe start"),
        PROBE_SUCCESS(4, "probe success"),
        PROBE_FAIL(5, "probe fail"),
        VIDEO_ESTABLISHED(6, "video established"),
        BYE_BYE(7, "bye bye")
    }
}