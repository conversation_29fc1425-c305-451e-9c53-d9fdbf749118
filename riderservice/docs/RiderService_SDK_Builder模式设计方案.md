# RiderService SDK Builder模式设计方案

**版本**: 2.2 | **状态**: 设计方案 | **类型**: 架构优化

---

## 目录

- [Builder模式优势分析](#builder模式优势分析)
- [Builder设计方案](#builder设计方案)
- [配置类定义](#配置类定义)
- [Builder实现](#builder实现)
- [使用示例](#使用示例)
- [与现有架构集成](#与现有架构集成)
- [迁移方案](#迁移方案)

---

## Builder模式优势分析

### 1. 为什么需要Builder模式

**当前初始化方式的问题:**
```kotlin
// 当前方式：配置分散，不够灵活
val riderService = RiderService.getInstance()
riderService.init(application) // 只能传入Application，其他配置需要后续设置
riderService.connection.setWifiConnectionMode(WifiConnectionMode.WIFI_AP_CLIENT)
// 其他配置需要分别设置...
```

**Builder模式的优势:**
1. **配置集中化**: 所有初始化配置在一个地方完成
2. **类型安全**: 编译时检查配置有效性
3. **可读性强**: 链式调用，配置意图清晰
4. **扩展性好**: 新增配置不破坏现有API
5. **默认值**: 提供合理的默认配置
6. **验证机制**: build()时进行配置验证

### 2. 适用场景分析

**RiderService SDK的配置需求:**
- WiFi连接模式选择
- 连接超时设置
- 重连策略配置
- 日志级别控制
- 调试模式开关
- 用户偏好设置
- 性能优化选项

**结论**: Builder模式非常适合RiderService SDK，能显著提升开发体验。

---

## Builder设计方案

### 1. 整体架构

```mermaid
graph TB
    subgraph Builder["Builder模式架构"]
        App[Android应用]
        
        subgraph BuilderAPI["Builder API"]
            RiderServiceBuilder[RiderServiceBuilder<br/>配置构建器]
            RiderServiceConfig[RiderServiceConfig<br/>配置数据类]
        end
        
        subgraph Core["核心SDK"]
            RiderServiceImpl[RiderServiceImpl<br/>SDK实现]
            ConnectionCore[ConnectionCore]
            MessagingCore[MessagingCore]
        end
    end
    
    App --> RiderServiceBuilder
    RiderServiceBuilder --> RiderServiceConfig
    RiderServiceBuilder --> RiderServiceImpl
    RiderServiceImpl --> ConnectionCore
    RiderServiceImpl --> MessagingCore
    
    classDef builder fill:#4caf50,stroke:#388e3c,stroke-width:2px,color:#ffffff
    classDef config fill:#ff9800,stroke:#f57c00,stroke-width:2px,color:#ffffff
    classDef core fill:#2196f3,stroke:#1976d2,stroke-width:2px,color:#ffffff
    
    class RiderServiceBuilder,RiderServiceConfig builder
    class RiderServiceImpl,ConnectionCore,MessagingCore core
```

### 2. 核心设计原则

1. **流畅接口**: 支持链式调用
2. **不可变配置**: 配置对象创建后不可修改
3. **验证机制**: build()时验证配置有效性
4. **默认值**: 所有配置都有合理默认值
5. **类型安全**: 使用强类型避免配置错误

---

## 配置类定义

### 1. 主配置类

```kotlin
data class RiderServiceConfig(
    // === 基础配置 ===
    val application: Application,
    val debugMode: Boolean = false,
    val logLevel: LogLevel = LogLevel.INFO,
    
    // === 连接配置 ===
    val connectionConfig: ConnectionConfig = ConnectionConfig(),
    
    // === 消息配置 ===
    val messagingConfig: MessagingConfig = MessagingConfig(),
    
    // === 导航配置 ===
    val navigationConfig: NavigationConfig = NavigationConfig(),
    
    // === 投屏配置 ===
    val projectionConfig: ProjectionConfig = ProjectionConfig(),
    
    // === 用户偏好 ===
    val userPreferences: UserPreferences = UserPreferences()
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        errors.addAll(connectionConfig.validate())
        errors.addAll(messagingConfig.validate())
        errors.addAll(navigationConfig.validate())
        errors.addAll(projectionConfig.validate())
        errors.addAll(userPreferences.validate())
        return errors
    }
}

enum class LogLevel {
    VERBOSE, DEBUG, INFO, WARN, ERROR, NONE
}
```

### 2. 连接配置

```kotlin
data class ConnectionConfig(
    // === WiFi配置 ===
    val wifiMode: WifiConnectionMode = WifiConnectionMode.WIFI_AP_CLIENT,
    val wifiAutoConnect: Boolean = true,
    val wifiConnectionTimeout: Duration = 30.seconds,
    
    // === BLE配置 ===
    val bleScanTimeout: Duration = 10.seconds,
    val bleConnectionTimeout: Duration = 15.seconds,
    val bleAutoReconnect: Boolean = true,
    
    // === 重连策略 ===
    val retryPolicy: RetryPolicy = RetryPolicy(),
    
    // === 高级配置 ===
    val enableConnectionMetrics: Boolean = false,
    val connectionPoolSize: Int = 1
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        
        if (wifiConnectionTimeout.inWholeSeconds < 5) {
            errors.add("WiFi connection timeout must be at least 5 seconds")
        }
        
        if (bleScanTimeout.inWholeSeconds < 1) {
            errors.add("BLE scan timeout must be at least 1 second")
        }
        
        return errors
    }
}
```

### 3. 消息配置

```kotlin
data class MessagingConfig(
    // === 队列配置 ===
    val maxQueueSize: Int = 100,
    val messageTimeout: Duration = 5.seconds,
    val enableMessagePersistence: Boolean = false,

    // === 发送策略 ===
    val batchSending: Boolean = false,
    val batchSize: Int = 10,
    val batchInterval: Duration = 100.milliseconds,

    // === 优先级配置 ===
    val enablePriorityQueue: Boolean = true,
    val highPriorityTypes: Set<String> = setOf("NaviInfo", "GpsSignal"),

    // === 压缩配置 ===
    val enableCompression: Boolean = false,
    val compressionThreshold: Int = 1024 // bytes
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (maxQueueSize <= 0) {
            errors.add("Max queue size must be positive")
        }
        if (batchSize <= 0 || batchSize > maxQueueSize) {
            errors.add("Batch size must be positive and not exceed max queue size")
        }
        if (compressionThreshold <= 0) {
            errors.add("Compression threshold must be positive")
        }
        return errors
    }
}
```

### 4. 导航配置

```kotlin
data class NavigationConfig(
    // === 默认模式 ===
    val defaultMode: NaviMode = NaviMode.SimpleNavi,
    val autoStartNavigation: Boolean = false,

    // === 模式切换 ===
    val allowModeSwitch: Boolean = true,
    val modeSwitchTimeout: Duration = 10.seconds,

    // === 数据更新 ===
    val naviUpdateInterval: Duration = 1.seconds,
    val enableRouteOptimization: Boolean = true,

    // === 导航行为 ===
    val enableVoiceGuidance: Boolean = true,
    val enableLaneGuidance: Boolean = true,
    val enableSpeedLimit: Boolean = true,
    val enableTrafficInfo: Boolean = true,

    // === 显示设置 ===
    val mapOrientation: MapOrientation = MapOrientation.NORTH_UP,
    val zoomLevel: Int = 15,
    val enableNightMode: Boolean = false
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (modeSwitchTimeout.inWholeSeconds < 1) {
            errors.add("Mode switch timeout must be at least 1 second")
        }
        if (naviUpdateInterval.inWholeMilliseconds < 100) {
            errors.add("Navigation update interval must be at least 100ms")
        }
        if (zoomLevel < 1 || zoomLevel > 20) {
            errors.add("Zoom level must be between 1 and 20")
        }
        return errors
    }
}

enum class MapOrientation {
    NORTH_UP,
    HEADING_UP,
    COURSE_UP
}
```

### 5. 投屏配置

```kotlin
data class ProjectionConfig(
    // === 显示配置 ===
    val enableAutoProjection: Boolean = false,
    val projectionQuality: ProjectionQuality = ProjectionQuality.MEDIUM,
    val frameRate: Int = 30,
    val resolution: Resolution = Resolution.HD_720P,

    // === 镜像配置 ===
    val enableMirrorMode: Boolean = true,
    val mirrorOrientation: MirrorOrientation = MirrorOrientation.AUTO,
    val enableAudioMirror: Boolean = false,

    // === 性能配置 ===
    val enableHardwareAcceleration: Boolean = true,
    val bufferSize: Int = 1024 * 1024, // 1MB
    val maxBitrate: Int = 8000, // kbps
    val enableAdaptiveBitrate: Boolean = true,

    // === 显示设置 ===
    val brightness: Float = 1.0f, // 0.0 - 1.0
    val contrast: Float = 1.0f,   // 0.0 - 2.0
    val saturation: Float = 1.0f, // 0.0 - 2.0

    // === 交互配置 ===
    val enableTouchInput: Boolean = true,
    val enableGestureControl: Boolean = false,
    val touchSensitivity: Float = 1.0f // 0.1 - 2.0
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (frameRate <= 0 || frameRate > 60) {
            errors.add("Frame rate must be between 1 and 60")
        }
        if (bufferSize <= 0) {
            errors.add("Buffer size must be positive")
        }
        if (maxBitrate <= 0) {
            errors.add("Max bitrate must be positive")
        }
        if (brightness < 0.0f || brightness > 1.0f) {
            errors.add("Brightness must be between 0.0 and 1.0")
        }
        if (contrast < 0.0f || contrast > 2.0f) {
            errors.add("Contrast must be between 0.0 and 2.0")
        }
        if (saturation < 0.0f || saturation > 2.0f) {
            errors.add("Saturation must be between 0.0 and 2.0")
        }
        if (touchSensitivity < 0.1f || touchSensitivity > 2.0f) {
            errors.add("Touch sensitivity must be between 0.1 and 2.0")
        }
        return errors
    }
}

enum class ProjectionQuality {
    LOW, MEDIUM, HIGH, ULTRA
}

enum class Resolution {
    HD_720P, FHD_1080P, QHD_1440P, UHD_4K
}

enum class MirrorOrientation {
    AUTO, PORTRAIT, LANDSCAPE, REVERSE_PORTRAIT, REVERSE_LANDSCAPE
}
```

### 6. 用户偏好配置

```kotlin
data class UserPreferences(
    // === 导航偏好 ===
    val autoStartNavigation: Boolean = false,
    val defaultNavigationMode: NaviMode = NaviMode.SimpleNavi,
    val preferredMapStyle: MapStyle = MapStyle.STANDARD,

    // === 通知偏好 ===
    val enableNotifications: Boolean = true,
    val notificationTypes: Set<NotificationType> = setOf(
        NotificationType.NAVIGATION,
        NotificationType.TRAFFIC,
        NotificationType.WEATHER
    ),
    val notificationSound: Boolean = true,
    val notificationVibration: Boolean = true,

    // === 显示偏好 ===
    val theme: Theme = Theme.AUTO,
    val fontSize: FontSize = FontSize.MEDIUM,
    val language: String = "zh-CN",
    val units: Units = Units.METRIC,

    // === 连接偏好 ===
    val preferredWifiMode: WifiConnectionMode = WifiConnectionMode.WIFI_AP_CLIENT,
    val autoConnectLastDevice: Boolean = true,
    val connectionTimeout: Duration = 30.seconds,

    // === 隐私偏好 ===
    val enableAnalytics: Boolean = true,
    val enableCrashReporting: Boolean = true,
    val enableLocationSharing: Boolean = false,

    // === 性能偏好 ===
    val enablePowerSaving: Boolean = false,
    val backgroundDataUsage: DataUsageLevel = DataUsageLevel.NORMAL,
    val cacheSize: Int = 100 // MB
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (connectionTimeout.inWholeSeconds < 5) {
            errors.add("Connection timeout must be at least 5 seconds")
        }
        if (cacheSize <= 0 || cacheSize > 1000) {
            errors.add("Cache size must be between 1 and 1000 MB")
        }
        return errors
    }
}

enum class MapStyle {
    STANDARD, SATELLITE, TERRAIN, HYBRID
}

enum class NotificationType {
    NAVIGATION, TRAFFIC, WEATHER, SYSTEM, MESSAGE
}

enum class Theme {
    LIGHT, DARK, AUTO
}

enum class FontSize {
    SMALL, MEDIUM, LARGE, EXTRA_LARGE
}

enum class Units {
    METRIC, IMPERIAL
}

enum class DataUsageLevel {
    LOW, NORMAL, HIGH, UNLIMITED
}
```

### 7. 重试策略配置

```kotlin
data class RetryPolicy(
    val maxRetries: Int = 3,
    val initialDelayMs: Long = 1000,
    val maxDelayMs: Long = 10000,
    val backoffMultiplier: Double = 2.0,
    val enableJitter: Boolean = true
) {
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (maxRetries < 0) {
            errors.add("Max retries must be non-negative")
        }
        if (initialDelayMs <= 0) {
            errors.add("Initial delay must be positive")
        }
        if (maxDelayMs <= initialDelayMs) {
            errors.add("Max delay must be greater than initial delay")
        }
        if (backoffMultiplier <= 1.0) {
            errors.add("Backoff multiplier must be greater than 1.0")
        }
        return errors
    }
}
```

---

## Builder实现

### 1. RiderServiceBuilder主类

```kotlin
class RiderServiceBuilder private constructor() {
    private var application: Application? = null
    private var debugMode: Boolean = false
    private var logLevel: LogLevel = LogLevel.INFO
    
    private var connectionConfigBuilder: ConnectionConfigBuilder = ConnectionConfigBuilder()
    private var messagingConfigBuilder: MessagingConfigBuilder = MessagingConfigBuilder()
    private var navigationConfigBuilder: NavigationConfigBuilder = NavigationConfigBuilder()
    private var projectionConfigBuilder: ProjectionConfigBuilder = ProjectionConfigBuilder()
    private var userPreferencesBuilder: UserPreferencesBuilder = UserPreferencesBuilder()

    companion object {
        /**
         * 创建Builder实例
         */
        fun newBuilder(): RiderServiceBuilder = RiderServiceBuilder()
        
        /**
         * 使用默认配置快速创建
         */
        fun createDefault(application: Application): RiderService {
            return newBuilder()
                .application(application)
                .build()
        }
    }

    // === 基础配置 ===
    fun application(application: Application): RiderServiceBuilder {
        this.application = application
        return this
    }

    fun debugMode(enabled: Boolean): RiderServiceBuilder {
        this.debugMode = enabled
        return this
    }

    fun logLevel(level: LogLevel): RiderServiceBuilder {
        this.logLevel = level
        return this
    }

    // === 连接配置 ===
    fun connection(block: ConnectionConfigBuilder.() -> Unit): RiderServiceBuilder {
        connectionConfigBuilder.apply(block)
        return this
    }

    // === 消息配置 ===
    fun messaging(block: MessagingConfigBuilder.() -> Unit): RiderServiceBuilder {
        messagingConfigBuilder.apply(block)
        return this
    }

    // === 导航配置 ===
    fun navigation(block: NavigationConfigBuilder.() -> Unit): RiderServiceBuilder {
        navigationConfigBuilder.apply(block)
        return this
    }

    // === 投屏配置 ===
    fun projection(block: ProjectionConfigBuilder.() -> Unit): RiderServiceBuilder {
        projectionConfigBuilder.apply(block)
        return this
    }

    // === 用户偏好 ===
    fun userPreferences(block: UserPreferencesBuilder.() -> Unit): RiderServiceBuilder {
        userPreferencesBuilder.apply(block)
        return this
    }

    /**
     * 构建RiderService实例
     */
    fun build(): RiderService {
        val app = application ?: throw IllegalStateException("Application is required")
        
        val config = RiderServiceConfig(
            application = app,
            debugMode = debugMode,
            logLevel = logLevel,
            connectionConfig = connectionConfigBuilder.build(),
            messagingConfig = messagingConfigBuilder.build(),
            navigationConfig = navigationConfigBuilder.build(),
            projectionConfig = projectionConfigBuilder.build(),
            userPreferences = userPreferencesBuilder.build()
        )
        
        // 验证配置
        val errors = config.validate()
        if (errors.isNotEmpty()) {
            throw IllegalArgumentException("Invalid configuration: ${errors.joinToString(", ")}")
        }
        
        return RiderServiceImpl.create(config)
    }
}
```

### 2. 子配置Builder类

```kotlin
class ConnectionConfigBuilder {
    private var wifiMode: WifiConnectionMode = WifiConnectionMode.WIFI_AP_CLIENT
    private var wifiAutoConnect: Boolean = true
    private var wifiConnectionTimeout: Duration = 30.seconds
    private var bleScanTimeout: Duration = 10.seconds
    private var bleConnectionTimeout: Duration = 15.seconds
    private var bleAutoReconnect: Boolean = true
    private var retryPolicy: RetryPolicy = RetryPolicy()
    private var enableConnectionMetrics: Boolean = false

    fun wifiMode(mode: WifiConnectionMode): ConnectionConfigBuilder {
        this.wifiMode = mode
        return this
    }

    fun wifiAutoConnect(enabled: Boolean): ConnectionConfigBuilder {
        this.wifiAutoConnect = enabled
        return this
    }

    fun wifiTimeout(timeout: Duration): ConnectionConfigBuilder {
        this.wifiConnectionTimeout = timeout
        return this
    }

    fun bleTimeout(scanTimeout: Duration, connectionTimeout: Duration): ConnectionConfigBuilder {
        this.bleScanTimeout = scanTimeout
        this.bleConnectionTimeout = connectionTimeout
        return this
    }

    fun autoReconnect(enabled: Boolean): ConnectionConfigBuilder {
        this.bleAutoReconnect = enabled
        return this
    }

    fun retryPolicy(policy: RetryPolicy): ConnectionConfigBuilder {
        this.retryPolicy = policy
        return this
    }

    fun enableMetrics(enabled: Boolean): ConnectionConfigBuilder {
        this.enableConnectionMetrics = enabled
        return this
    }

    internal fun build(): ConnectionConfig {
        return ConnectionConfig(
            wifiMode = wifiMode,
            wifiAutoConnect = wifiAutoConnect,
            wifiConnectionTimeout = wifiConnectionTimeout,
            bleScanTimeout = bleScanTimeout,
            bleConnectionTimeout = bleConnectionTimeout,
            bleAutoReconnect = bleAutoReconnect,
            retryPolicy = retryPolicy,
            enableConnectionMetrics = enableConnectionMetrics
        )
    }
}

class MessagingConfigBuilder {
    private var maxQueueSize: Int = 100
    private var messageTimeout: Duration = 5.seconds
    private var enableMessagePersistence: Boolean = false
    private var batchSending: Boolean = false
    private var batchSize: Int = 10
    private var enablePriorityQueue: Boolean = true
    private var enableCompression: Boolean = false

    fun queueSize(size: Int): MessagingConfigBuilder {
        this.maxQueueSize = size
        return this
    }

    fun messageTimeout(timeout: Duration): MessagingConfigBuilder {
        this.messageTimeout = timeout
        return this
    }

    fun enablePersistence(enabled: Boolean): MessagingConfigBuilder {
        this.enableMessagePersistence = enabled
        return this
    }

    fun batchSending(enabled: Boolean, batchSize: Int = 10): MessagingConfigBuilder {
        this.batchSending = enabled
        this.batchSize = batchSize
        return this
    }

    fun enablePriorityQueue(enabled: Boolean): MessagingConfigBuilder {
        this.enablePriorityQueue = enabled
        return this
    }

    fun enableCompression(enabled: Boolean): MessagingConfigBuilder {
        this.enableCompression = enabled
        return this
    }

    internal fun build(): MessagingConfig {
        return MessagingConfig(
            maxQueueSize = maxQueueSize,
            messageTimeout = messageTimeout,
            enableMessagePersistence = enableMessagePersistence,
            batchSending = batchSending,
            batchSize = batchSize,
            enablePriorityQueue = enablePriorityQueue,
            enableCompression = enableCompression
        )
    }
}

class NavigationConfigBuilder {
    private var defaultMode: NaviMode = NaviMode.SimpleNavi
    private var autoStartNavigation: Boolean = false
    private var allowModeSwitch: Boolean = true
    private var modeSwitchTimeout: Duration = 10.seconds
    private var naviUpdateInterval: Duration = 1.seconds
    private var enableRouteOptimization: Boolean = true
    private var enableVoiceGuidance: Boolean = true
    private var enableLaneGuidance: Boolean = true
    private var enableSpeedLimit: Boolean = true
    private var enableTrafficInfo: Boolean = true
    private var mapOrientation: MapOrientation = MapOrientation.NORTH_UP
    private var zoomLevel: Int = 15
    private var enableNightMode: Boolean = false

    fun defaultMode(mode: NaviMode): NavigationConfigBuilder {
        this.defaultMode = mode
        return this
    }

    fun autoStart(enabled: Boolean): NavigationConfigBuilder {
        this.autoStartNavigation = enabled
        return this
    }

    fun allowModeSwitch(enabled: Boolean): NavigationConfigBuilder {
        this.allowModeSwitch = enabled
        return this
    }

    fun modeSwitchTimeout(timeout: Duration): NavigationConfigBuilder {
        this.modeSwitchTimeout = timeout
        return this
    }

    fun updateInterval(interval: Duration): NavigationConfigBuilder {
        this.naviUpdateInterval = interval
        return this
    }

    fun enableRouteOptimization(enabled: Boolean): NavigationConfigBuilder {
        this.enableRouteOptimization = enabled
        return this
    }

    fun enableVoiceGuidance(enabled: Boolean): NavigationConfigBuilder {
        this.enableVoiceGuidance = enabled
        return this
    }

    fun enableLaneGuidance(enabled: Boolean): NavigationConfigBuilder {
        this.enableLaneGuidance = enabled
        return this
    }

    fun enableSpeedLimit(enabled: Boolean): NavigationConfigBuilder {
        this.enableSpeedLimit = enabled
        return this
    }

    fun enableTrafficInfo(enabled: Boolean): NavigationConfigBuilder {
        this.enableTrafficInfo = enabled
        return this
    }

    fun mapOrientation(orientation: MapOrientation): NavigationConfigBuilder {
        this.mapOrientation = orientation
        return this
    }

    fun zoomLevel(level: Int): NavigationConfigBuilder {
        this.zoomLevel = level
        return this
    }

    fun enableNightMode(enabled: Boolean): NavigationConfigBuilder {
        this.enableNightMode = enabled
        return this
    }

    internal fun build(): NavigationConfig {
        return NavigationConfig(
            defaultMode = defaultMode,
            autoStartNavigation = autoStartNavigation,
            allowModeSwitch = allowModeSwitch,
            modeSwitchTimeout = modeSwitchTimeout,
            naviUpdateInterval = naviUpdateInterval,
            enableRouteOptimization = enableRouteOptimization,
            enableVoiceGuidance = enableVoiceGuidance,
            enableLaneGuidance = enableLaneGuidance,
            enableSpeedLimit = enableSpeedLimit,
            enableTrafficInfo = enableTrafficInfo,
            mapOrientation = mapOrientation,
            zoomLevel = zoomLevel,
            enableNightMode = enableNightMode
        )
    }
}

class ProjectionConfigBuilder {
    private var enableAutoProjection: Boolean = false
    private var projectionQuality: ProjectionQuality = ProjectionQuality.MEDIUM
    private var frameRate: Int = 30
    private var resolution: Resolution = Resolution.HD_720P
    private var enableMirrorMode: Boolean = true
    private var mirrorOrientation: MirrorOrientation = MirrorOrientation.AUTO
    private var enableAudioMirror: Boolean = false
    private var enableHardwareAcceleration: Boolean = true
    private var bufferSize: Int = 1024 * 1024
    private var maxBitrate: Int = 8000
    private var enableAdaptiveBitrate: Boolean = true
    private var brightness: Float = 1.0f
    private var contrast: Float = 1.0f
    private var saturation: Float = 1.0f
    private var enableTouchInput: Boolean = true
    private var enableGestureControl: Boolean = false
    private var touchSensitivity: Float = 1.0f

    fun autoProjection(enabled: Boolean): ProjectionConfigBuilder {
        this.enableAutoProjection = enabled
        return this
    }

    fun quality(quality: ProjectionQuality): ProjectionConfigBuilder {
        this.projectionQuality = quality
        return this
    }

    fun frameRate(rate: Int): ProjectionConfigBuilder {
        this.frameRate = rate
        return this
    }

    fun resolution(res: Resolution): ProjectionConfigBuilder {
        this.resolution = res
        return this
    }

    fun enableMirror(enabled: Boolean): ProjectionConfigBuilder {
        this.enableMirrorMode = enabled
        return this
    }

    fun mirrorOrientation(orientation: MirrorOrientation): ProjectionConfigBuilder {
        this.mirrorOrientation = orientation
        return this
    }

    fun enableAudioMirror(enabled: Boolean): ProjectionConfigBuilder {
        this.enableAudioMirror = enabled
        return this
    }

    fun enableHardwareAcceleration(enabled: Boolean): ProjectionConfigBuilder {
        this.enableHardwareAcceleration = enabled
        return this
    }

    fun bufferSize(size: Int): ProjectionConfigBuilder {
        this.bufferSize = size
        return this
    }

    fun maxBitrate(bitrate: Int): ProjectionConfigBuilder {
        this.maxBitrate = bitrate
        return this
    }

    fun enableAdaptiveBitrate(enabled: Boolean): ProjectionConfigBuilder {
        this.enableAdaptiveBitrate = enabled
        return this
    }

    fun displaySettings(brightness: Float, contrast: Float, saturation: Float): ProjectionConfigBuilder {
        this.brightness = brightness
        this.contrast = contrast
        this.saturation = saturation
        return this
    }

    fun touchSettings(enableTouch: Boolean, enableGesture: Boolean, sensitivity: Float): ProjectionConfigBuilder {
        this.enableTouchInput = enableTouch
        this.enableGestureControl = enableGesture
        this.touchSensitivity = sensitivity
        return this
    }

    internal fun build(): ProjectionConfig {
        return ProjectionConfig(
            enableAutoProjection = enableAutoProjection,
            projectionQuality = projectionQuality,
            frameRate = frameRate,
            resolution = resolution,
            enableMirrorMode = enableMirrorMode,
            mirrorOrientation = mirrorOrientation,
            enableAudioMirror = enableAudioMirror,
            enableHardwareAcceleration = enableHardwareAcceleration,
            bufferSize = bufferSize,
            maxBitrate = maxBitrate,
            enableAdaptiveBitrate = enableAdaptiveBitrate,
            brightness = brightness,
            contrast = contrast,
            saturation = saturation,
            enableTouchInput = enableTouchInput,
            enableGestureControl = enableGestureControl,
            touchSensitivity = touchSensitivity
        )
    }
}

class UserPreferencesBuilder {
    private var autoStartNavigation: Boolean = false
    private var defaultNavigationMode: NaviMode = NaviMode.SimpleNavi
    private var preferredMapStyle: MapStyle = MapStyle.STANDARD
    private var enableNotifications: Boolean = true
    private var notificationTypes: Set<NotificationType> = setOf(
        NotificationType.NAVIGATION,
        NotificationType.TRAFFIC,
        NotificationType.WEATHER
    )
    private var notificationSound: Boolean = true
    private var notificationVibration: Boolean = true
    private var theme: Theme = Theme.AUTO
    private var fontSize: FontSize = FontSize.MEDIUM
    private var language: String = "zh-CN"
    private var units: Units = Units.METRIC
    private var preferredWifiMode: WifiConnectionMode = WifiConnectionMode.WIFI_AP_CLIENT
    private var autoConnectLastDevice: Boolean = true
    private var connectionTimeout: Duration = 30.seconds
    private var enableAnalytics: Boolean = true
    private var enableCrashReporting: Boolean = true
    private var enableLocationSharing: Boolean = false
    private var enablePowerSaving: Boolean = false
    private var backgroundDataUsage: DataUsageLevel = DataUsageLevel.NORMAL
    private var cacheSize: Int = 100

    fun autoStartNavigation(enabled: Boolean): UserPreferencesBuilder {
        this.autoStartNavigation = enabled
        return this
    }

    fun defaultNavigationMode(mode: NaviMode): UserPreferencesBuilder {
        this.defaultNavigationMode = mode
        return this
    }

    fun preferredMapStyle(style: MapStyle): UserPreferencesBuilder {
        this.preferredMapStyle = style
        return this
    }

    fun notifications(enabled: Boolean, types: Set<NotificationType> = notificationTypes): UserPreferencesBuilder {
        this.enableNotifications = enabled
        this.notificationTypes = types
        return this
    }

    fun notificationSettings(sound: Boolean, vibration: Boolean): UserPreferencesBuilder {
        this.notificationSound = sound
        this.notificationVibration = vibration
        return this
    }

    fun displaySettings(theme: Theme, fontSize: FontSize, language: String): UserPreferencesBuilder {
        this.theme = theme
        this.fontSize = fontSize
        this.language = language
        return this
    }

    fun units(units: Units): UserPreferencesBuilder {
        this.units = units
        return this
    }

    fun connectionSettings(wifiMode: WifiConnectionMode, autoConnect: Boolean, timeout: Duration): UserPreferencesBuilder {
        this.preferredWifiMode = wifiMode
        this.autoConnectLastDevice = autoConnect
        this.connectionTimeout = timeout
        return this
    }

    fun privacySettings(analytics: Boolean, crashReporting: Boolean, locationSharing: Boolean): UserPreferencesBuilder {
        this.enableAnalytics = analytics
        this.enableCrashReporting = crashReporting
        this.enableLocationSharing = locationSharing
        return this
    }

    fun performanceSettings(powerSaving: Boolean, dataUsage: DataUsageLevel, cacheSize: Int): UserPreferencesBuilder {
        this.enablePowerSaving = powerSaving
        this.backgroundDataUsage = dataUsage
        this.cacheSize = cacheSize
        return this
    }

    internal fun build(): UserPreferences {
        return UserPreferences(
            autoStartNavigation = autoStartNavigation,
            defaultNavigationMode = defaultNavigationMode,
            preferredMapStyle = preferredMapStyle,
            enableNotifications = enableNotifications,
            notificationTypes = notificationTypes,
            notificationSound = notificationSound,
            notificationVibration = notificationVibration,
            theme = theme,
            fontSize = fontSize,
            language = language,
            units = units,
            preferredWifiMode = preferredWifiMode,
            autoConnectLastDevice = autoConnectLastDevice,
            connectionTimeout = connectionTimeout,
            enableAnalytics = enableAnalytics,
            enableCrashReporting = enableCrashReporting,
            enableLocationSharing = enableLocationSharing,
            enablePowerSaving = enablePowerSaving,
            backgroundDataUsage = backgroundDataUsage,
            cacheSize = cacheSize
        )
    }
}
```

---

## 使用示例

### 1. 基础使用示例

```kotlin
class MainActivity : AppCompatActivity() {
    private lateinit var riderService: RiderService

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // === 方式1：使用默认配置 ===
        riderService = RiderServiceBuilder.createDefault(application)

        // === 方式2：自定义配置 ===
        riderService = RiderServiceBuilder.newBuilder()
            .application(application)
            .debugMode(BuildConfig.DEBUG)
            .logLevel(LogLevel.DEBUG)
            .build()
    }
}
```

### 2. 详细配置示例

```kotlin
class AdvancedMainActivity : AppCompatActivity() {
    private lateinit var riderService: RiderService

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        riderService = RiderServiceBuilder.newBuilder()
            .application(application)
            .debugMode(BuildConfig.DEBUG)
            .logLevel(if (BuildConfig.DEBUG) LogLevel.DEBUG else LogLevel.INFO)

            // 连接配置
            .connection {
                wifiMode(WifiConnectionMode.WIFI_AP_CLIENT)
                wifiAutoConnect(true)
                wifiTimeout(30.seconds)
                bleTimeout(scanTimeout = 15.seconds, connectionTimeout = 20.seconds)
                autoReconnect(true)
                retryPolicy(RetryPolicy(maxRetries = 3, initialDelayMs = 1000))
                enableMetrics(BuildConfig.DEBUG)
            }

            // 消息配置
            .messaging {
                queueSize(200)
                messageTimeout(10.seconds)
                enablePersistence(true)
                batchSending(enabled = true, batchSize = 5)
                enablePriorityQueue(true)
                enableCompression(true)
            }

            // 导航配置
            .navigation {
                defaultMode(NaviMode.SimpleNavi)
                autoStart(false)
                allowModeSwitch(true)
                modeSwitchTimeout(15.seconds)
                updateInterval(500.milliseconds)
                enableVoiceGuidance(true)
                enableLaneGuidance(true)
                mapOrientation(MapOrientation.HEADING_UP)
                zoomLevel(16)
                enableNightMode(true)
            }

            // 投屏配置
            .projection {
                autoProjection(false)
                quality(ProjectionQuality.HIGH)
                frameRate(60)
                resolution(Resolution.FHD_1080P)
                enableMirror(true)
                mirrorOrientation(MirrorOrientation.LANDSCAPE)
                enableAudioMirror(true)
                enableHardwareAcceleration(true)
                maxBitrate(10000)
                enableAdaptiveBitrate(true)
                displaySettings(brightness = 0.8f, contrast = 1.1f, saturation = 1.0f)
                touchSettings(enableTouch = true, enableGesture = true, sensitivity = 1.2f)
            }

            // 用户偏好
            .userPreferences {
                autoStartNavigation(false)
                defaultNavigationMode(NaviMode.SimpleNavi)
                preferredMapStyle(MapStyle.SATELLITE)
                notifications(enabled = true, types = setOf(
                    NotificationType.NAVIGATION,
                    NotificationType.TRAFFIC
                ))
                notificationSettings(sound = true, vibration = false)
                displaySettings(theme = Theme.DARK, fontSize = FontSize.LARGE, language = "zh-CN")
                units(Units.METRIC)
                connectionSettings(
                    wifiMode = WifiConnectionMode.WIFI_AP_CLIENT,
                    autoConnect = true,
                    timeout = 45.seconds
                )
                privacySettings(analytics = true, crashReporting = true, locationSharing = false)
                performanceSettings(
                    powerSaving = false,
                    dataUsage = DataUsageLevel.HIGH,
                    cacheSize = 200
                )
            }

            .build()
    }
}
```

### 3. 不同场景的配置示例

```kotlin
// === 场景1：性能优先配置 ===
val performanceOptimizedService = RiderServiceBuilder.newBuilder()
    .application(application)
    .connection {
        wifiMode(WifiConnectionMode.WIFI_P2P) // P2P模式更快
        wifiTimeout(15.seconds) // 更短的超时
        enableMetrics(false) // 关闭指标收集
    }
    .messaging {
        queueSize(50) // 较小的队列
        batchSending(enabled = true, batchSize = 20) // 批量发送
        enableCompression(true) // 启用压缩
    }
    .projection {
        quality(ProjectionQuality.MEDIUM) // 中等质量
        frameRate(30) // 标准帧率
    }
    .build()

// === 场景2：调试开发配置 ===
val debugService = RiderServiceBuilder.newBuilder()
    .application(application)
    .debugMode(true)
    .logLevel(LogLevel.VERBOSE)
    .connection {
        wifiTimeout(60.seconds) // 更长的超时便于调试
        enableMetrics(true) // 启用详细指标
    }
    .messaging {
        enablePersistence(true) // 持久化消息便于调试
        queueSize(500) // 大队列
    }
    .build()

// === 场景3：生产环境配置 ===
val productionService = RiderServiceBuilder.newBuilder()
    .application(application)
    .debugMode(false)
    .logLevel(LogLevel.WARN)
    .connection {
        wifiMode(WifiConnectionMode.WIFI_AP_CLIENT)
        autoReconnect(true)
        retryPolicy(RetryPolicy(maxRetries = 5, initialDelayMs = 2000))
    }
    .messaging {
        queueSize(100)
        enablePriorityQueue(true)
        batchSending(enabled = true, batchSize = 10)
    }
    .build()
```

### 4. 配置验证示例

```kotlin
try {
    val riderService = RiderServiceBuilder.newBuilder()
        .application(application)
        .connection {
            wifiTimeout((-1).seconds) // 无效配置
        }
        .messaging {
            queueSize(-10) // 无效配置
        }
        .build() // 这里会抛出异常
} catch (e: IllegalArgumentException) {
    Log.e("Config", "Configuration error: ${e.message}")
    // 使用默认配置作为后备
    val riderService = RiderServiceBuilder.createDefault(application)
}
```

---

## 与现有架构集成

### 1. RiderServiceImpl修改

```kotlin
internal class RiderServiceImpl private constructor(
    private val config: RiderServiceConfig
) : RiderService {

    // 管理器实例（使用配置初始化）
    override val connection: ConnectionManager by lazy {
        ConnectionManagerImpl(connectionCore, config.connectionConfig)
    }
    override val messaging: MessagingManager by lazy {
        MessagingManagerImpl(messagingCore, config.messagingConfig)
    }
    override val navigation: NavigationManager by lazy {
        NavigationManagerImpl(navigationCore, config.navigationConfig)
    }
    override val projection: ProjectionManager by lazy {
        ProjectionManagerImpl(projectionCore, config.projectionConfig)
    }
    override val config: ConfigManager by lazy {
        ConfigManagerImpl(configCore, config.userPreferences)
    }

    // 核心组件（使用配置初始化）
    private val connectionCore = ConnectionCore(config.connectionConfig)
    private val messagingCore = MessagingCore(config.messagingConfig)
    private val navigationCore = NavigationCore(config.navigationConfig)
    private val projectionCore = ProjectionCore(config.projectionConfig)
    private val configCore = ConfigCore(config.userPreferences)

    override fun init(application: Application): Result<Unit> {
        // 使用配置中的Application，忽略参数
        return try {
            initializeWithConfig()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun initializeWithConfig() {
        // 使用配置初始化各个核心组件
        configCore.init(config.application)
        connectionCore.init(config.application, messagingCore)
        messagingCore.init(connectionCore)
        navigationCore.init(connectionCore, messagingCore)
        projectionCore.init(config.application, connectionCore)

        // 应用用户偏好
        applyUserPreferences()
    }

    private fun applyUserPreferences() {
        val prefs = config.userPreferences
        if (prefs.autoStartNavigation) {
            // 自动启动导航
            lifecycleScope.launch {
                navigation.startNavigation(prefs.defaultNavigationMode)
            }
        }
    }

    companion object {
        @Volatile
        private var INSTANCE: RiderService? = null

        fun getInstance(): RiderService {
            return INSTANCE ?: throw IllegalStateException(
                "RiderService not initialized. Use RiderServiceBuilder to create instance."
            )
        }

        internal fun create(config: RiderServiceConfig): RiderService {
            return synchronized(this) {
                INSTANCE?.let {
                    throw IllegalStateException("RiderService already initialized")
                }
                val instance = RiderServiceImpl(config)
                INSTANCE = instance
                instance
            }
        }
    }
}
```

### 2. 向后兼容性支持

```kotlin
// 保持现有的简单初始化方式
object RiderService {
    @Deprecated("Use RiderServiceBuilder instead", ReplaceWith("RiderServiceBuilder.createDefault(application)"))
    fun getInstance(): RiderService {
        return RiderServiceImpl.getInstance()
    }

    @Deprecated("Use RiderServiceBuilder instead", ReplaceWith("RiderServiceBuilder.createDefault(application)"))
    fun init(application: Application): Result<Unit> {
        return try {
            val service = RiderServiceBuilder.createDefault(application)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
```

---

## 迁移方案

### 1. 迁移步骤

**阶段1: 添加Builder支持（1-2天）**
- 创建Builder类和配置类
- 修改RiderServiceImpl支持配置初始化
- 保持现有API的向后兼容性

**阶段2: 文档和示例更新（1天）**
- 更新API文档
- 提供迁移示例
- 创建最佳实践指南

**阶段3: 逐步迁移（根据项目需要）**
- 新项目直接使用Builder模式
- 现有项目可选择性迁移
- 旧API标记为Deprecated但继续支持

### 2. 迁移对照表

| 旧方式 | 新方式（Builder） | 优势 |
|--------|------------------|------|
| `RiderService.getInstance().init(app)` | `RiderServiceBuilder.createDefault(app)` | 配置集中化 |
| 分散的配置调用 | 链式配置调用 | 可读性更好 |
| 硬编码配置值 | 类型安全的配置 | 编译时检查 |
| 无配置验证 | 自动配置验证 | 减少运行时错误 |

### 3. 完整迁移示例

```kotlin
// === 旧方式 ===
class OldInitialization {
    fun initializeRiderService() {
        val riderService = RiderService.getInstance()
        riderService.init(application)

        // 分散的配置
        riderService.connection.setWifiConnectionMode(WifiConnectionMode.WIFI_AP_CLIENT)
        // 其他配置需要在不同地方设置...
    }
}

// === 新方式 ===
class NewInitialization {
    fun initializeRiderService() {
        val riderService = RiderServiceBuilder.newBuilder()
            .application(application)
            .debugMode(BuildConfig.DEBUG)
            .connection {
                wifiMode(WifiConnectionMode.WIFI_AP_CLIENT)
                wifiAutoConnect(true)
                wifiTimeout(30.seconds)
            }
            .messaging {
                queueSize(200)
                enablePriorityQueue(true)
            }
            .build()
    }
}
```

### 4. 渐进式迁移策略

```kotlin
// 步骤1：保持现有代码不变，添加Builder支持
// 现有代码继续工作
val oldService = RiderService.getInstance()
oldService.init(application)

// 步骤2：新功能使用Builder
val newService = RiderServiceBuilder.newBuilder()
    .application(application)
    .connection { wifiMode(WifiConnectionMode.WIFI_P2P) }
    .build()

// 步骤3：逐步迁移现有代码（可选）
// 根据项目需要决定是否迁移
```

---

## 总结

### 1. Builder模式的价值

**对于RiderService SDK，Builder模式带来的主要价值：**

1. **配置集中化**: 所有初始化配置在一个地方完成，避免分散配置
2. **类型安全**: 编译时检查配置有效性，减少运行时错误
3. **可读性**: 链式调用让配置意图清晰，代码更易理解
4. **扩展性**: 新增配置项不会破坏现有API，向后兼容性好
5. **验证机制**: 自动验证配置合理性，提前发现问题
6. **默认值**: 提供合理的默认配置，简化常见使用场景
7. **场景化配置**: 支持不同场景的预设配置（开发、测试、生产）

### 2. 实施建议

**推荐实施Builder模式的原因：**

1. **提升开发体验**: 配置更直观，减少配置错误
2. **便于维护**: 配置集中管理，易于理解和修改
3. **支持复杂场景**: 可以轻松支持不同的使用场景
4. **向后兼容**: 不会破坏现有代码，可以渐进式迁移
5. **行业标准**: 符合Android SDK的设计惯例

**实施优先级：高**
- Builder模式是现代SDK设计的标准做法
- 能显著提升开发者体验
- 实施成本相对较低
- 长期收益明显

### 3. 最终API对比

```kotlin
// === 当前方式 ===
val riderService = RiderService.getInstance()
riderService.init(application)
// 配置分散，不够直观

// === Builder方式 ===
val riderService = RiderServiceBuilder.newBuilder()
    .application(application)
    .debugMode(BuildConfig.DEBUG)
    .connection {
        wifiMode(WifiConnectionMode.WIFI_AP_CLIENT)
        autoReconnect(true)
        wifiTimeout(30.seconds)
    }
    .messaging {
        queueSize(200)
        enablePriorityQueue(true)
    }
    .build()
// 配置集中，意图清晰，类型安全
```

**结论**: Builder模式非常适合RiderService SDK，建议优先实施。它不仅提升了开发体验，还为未来的功能扩展提供了良好的基础架构。
