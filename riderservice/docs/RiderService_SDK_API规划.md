# RiderService SDK API 规划文档

## API 分类概览

基于对现有代码的分析，RiderService SDK 的 API 可以分为以下几个主要类别：

### 1. 生命周期管理 API

**功能描述**: 管理 SDK 的初始化、销毁和状态

| 方法名                | 参数            | 返回值           | 描述          |
|--------------------|---------------|---------------|-------------|
| `init()`           | `Application` | `void`        | 初始化 SDK     |
| `destroy()`        | -             | `void`        | 销毁 SDK，释放资源 |
| `getApplication()` | -             | `Application` | 获取应用实例      |

### 2. 连接管理 API

**功能描述**: 管理蓝牙和WiFi连接

#### 2.1 蓝牙连接

| 方法名                         | 参数                            | 返回值          | 描述       |
|-----------------------------|-------------------------------|--------------|----------|
| `startBleScan()`            | `Boolean` (shouldAutoConnect) | `void`       | 开始蓝牙扫描   |
| `stopBleScan()`             | -                             | `void`       | 停止蓝牙扫描   |
| `connect()`                 | `BleDevice`                   | `void`       | 连接指定蓝牙设备 |
| `disconnect()`              | `Boolean` (isManual)          | `void`       | 断开蓝牙连接   |
| `getConnectStatus()`        | -                             | `Connection` | 获取连接状态   |
| `getCurrentConnectDevice()` | -                             | `BleDevice?` | 获取当前连接设备 |

#### 2.2 WiFi连接

| 方法名                       | 参数                   | 返回值                  | 描述         |
|---------------------------|----------------------|----------------------|------------|
| `setWifiConnectionMode()` | `WifiConnectionMode` | `void`               | 设置WiFi连接模式 |
| `getWifiConnectionMode()` | -                    | `WifiConnectionMode` | 获取WiFi连接模式 |

### 3. 消息发送 API

**功能描述**: 向仪表盘发送各种类型的消息

#### 3.1 统一消息发送接口

| 方法名                           | 参数             | 返回值    | 描述       |
|-------------------------------|----------------|--------|----------|
| `sendMessageToRiderService()` | `RiderMessage` | `void` | 发送消息到仪表盘 |

#### 3.2 支持的消息类型

| 消息类型       | 数据类                  | 用途             |
|------------|----------------------|----------------|
| 导航信息       | `NaviInfo`           | 发送实时导航数据       |
| 通知信息       | `NotificationInfo`   | 发送应用通知         |
| 天气信息       | `WeatherInfo`        | 发送天气数据         |
| 车道信息       | `LaneInfo`           | 发送车道指引信息       |
| 路口信息       | `NaviCross`          | 发送路口图像数据       |
| 导航文本       | `NaviText`           | 发送导航文字提示       |
| 路线信息       | `NaviRoute`          | 发送路线规划信息       |
| GPS信号      | `GpsSignal`          | 发送GPS信号状态      |
| 导航控制       | `NaviStart/NaviStop` | 控制导航开始/停止      |
| 到达目的地      | `ArriveDestination`  | 通知到达目的地        |
| AutoLink连接 | `AutoLinkConnect`    | 发送AutoLink连接信息 |
| 导航版本请求     | `NaviVersionRequest` | 请求导航版本信息       |

### 4. 导航控制 API

**功能描述**: 控制导航模式和状态

| 方法名                            | 参数                     | 返回值       | 描述         |
|--------------------------------|------------------------|-----------|------------|
| `setNaviMode()`                | `NaviMode`             | `void`    | 设置导航模式     |
| `sendNaviModeChange()`         | `NaviMode`             | `void`    | 发送导航模式变更   |
| `sendNaviModeStart()`          | `NaviMode`             | `void`    | 发送导航模式开始   |
| `sendNaviModeStop()`           | `NaviMode`             | `void`    | 发送导航模式停止   |
| `sendNaviModeChangeResponse()` | `NaviModeChangeResult` | `void`    | 发送导航模式变更响应 |
| `needChangeDisplay()`          | -                      | `Boolean` | 检查是否需要切换显示 |

#### 4.1 导航模式类型

| 模式               | 描述         |
|------------------|------------|
| `NoNavi`         | 无导航        |
| `Default`        | 默认模式       |
| `SimpleNavi`     | 简易导航（蓝牙导航） |
| `ScreenNavi`     | 投屏导航（竖屏）   |
| `CruiseNAVI`     | 巡航导航（横屏）   |
| `MirrorNAVI`     | 镜像导航       |
| `LockScreenNavi` | 锁屏导航       |

### 5. 投屏控制 API

**功能描述**: 管理屏幕投射功能

| 方法名                          | 参数                    | 返回值    | 描述      |
|------------------------------|-----------------------|--------|---------|
| `setMediaProjection()`       | `MediaProjection`     | `void` | 设置媒体投射  |
| `requestLockScreenDisplay()` | -                     | `void` | 请求锁屏显示  |
| `initLockScreenProjection()` | `Display?`, `Boolean` | `void` | 初始化锁屏投射 |
| `startScreenNavi()`          | -                     | `void` | 开始投屏导航  |
| `stopScreenNavi()`           | -                     | `void` | 停止投屏导航  |

### 6. 配置管理 API

**功能描述**: 管理SDK配置和设备信息

| 方法名                      | 参数 | 返回值                 | 描述           |
|--------------------------|----|---------------------|--------------|
| `getConfigPreferences()` | -  | `ConfigPreferences` | 获取配置管理器      |
| `deleteConfig()`         | -  | `void`              | 清除自动互联配对     |
| `getProductKey()`        | -  | `String`            | 获取产品密钥       |
| `getUuid()`              | -  | `String`            | 获取设备UUID     |
| `getAutolinkVersion()`   | -  | `String`            | 获取AutoLink版本 |

### 7. 回调管理 API

**功能描述**: 管理事件回调

| 方法名                | 参数                     | 返回值    | 描述      |
|--------------------|------------------------|--------|---------|
| `addCallback()`    | `RiderServiceCallback` | `void` | 添加回调监听器 |
| `removeCallback()` | `RiderServiceCallback` | `void` | 移除回调监听器 |

#### 7.1 回调事件类型

| 回调方法                      | 参数                   | 描述       |
|---------------------------|----------------------|----------|
| `onScanResult()`          | `List<BleDevice>`    | 蓝牙扫描结果   |
| `onScanning()`            | -                    | 开始扫描     |
| `onScanFinish()`          | -                    | 扫描完成     |
| `onConnectStatusChange()` | `Connection`         | 连接状态变化   |
| `onConfigChange()`        | `RiderServiceConfig` | 仪表配置变化   |
| `onNaviModeChange()`      | `NaviMode`           | 导航模式变化   |
| `onDisplayInitialized()`  | `Display`            | 显示屏初始化   |
| `onDisplayReleased()`     | `Display`            | 显示屏释放    |
| `onVideoChannelReady()`   | -                    | 视频通道准备就绪 |
| `onRequestWeatherInfo()`  | -                    | 请求天气信息   |
| `onClusterReady()`        | -                    | 仪表准备就绪   |
| `onDialogShow()`          | `String`, `String`   | 显示对话框    |

### 8. 数据传输对象 (DTO)

#### 8.1 连接相关

- `Connection`: 连接状态信息
- `BleStatus`: 蓝牙连接状态
- `WifiStatus`: WiFi连接状态
- `BleDevice`: 蓝牙设备信息

#### 8.2 导航相关

- `NaviInfo`: 导航信息
- `NaviMode`: 导航模式
- `NaviRoute`: 路线信息
- `NaviCross`: 路口信息
- `NaviText`: 导航文本
- `LaneInfo`: 车道信息

#### 8.3 系统消息

- `NotificationInfo`: 通知信息
- `WeatherInfo`: 天气信息
- `GpsSignal`: GPS信号状态
- `RiderServiceConfig`: 仪表配置

#### 8.4 控制消息

- `NaviStart/NaviStop`: 导航控制
- `ArriveDestination`: 到达目的地
- `AutoLinkConnect`: AutoLink连接
- `NaviVersionRequest`: 版本请求

## API 使用统计

| 类别         | 方法数量   | 主要特点       |
|------------|--------|------------|
| **生命周期管理** | 3个     | 基础SDK管理    |
| **连接管理**   | 8个     | 蓝牙+WiFi双通道 |
| **消息发送**   | 1个统一接口 | 支持12种消息类型  |
| **导航控制**   | 6个     | 多模式导航支持    |
| **投屏控制**   | 5个     | 完整投屏方案     |
| **配置管理**   | 5个     | 设备信息管理     |
| **回调管理**   | 2个     | 事件驱动架构     |

## 详细API分类表

### 按功能模块分类

| 功能模块       | API方法                          | 访问级别     | 线程安全 | 备注             |
|------------|--------------------------------|----------|------|----------------|
| **生命周期**   | `init()`                       | Internal | ✓    | 内部调用，应用层通过单例获取 |
|            | `destroy()`                    | Public   | ✓    | 释放所有资源         |
|            | `getApplication()`             | Public   | ✓    | 获取应用上下文        |
| **蓝牙连接**   | `startBleScan()`               | Public   | ✓    | 支持自动连接参数       |
|            | `stopBleScan()`                | Public   | ✓    | 停止扫描           |
|            | `connect()`                    | Public   | ✓    | 手动连接指定设备       |
|            | `disconnect()`                 | Public   | ✓    | 支持手动/自动断开标识    |
|            | `getConnectStatus()`           | Public   | ✓    | 返回详细连接状态       |
|            | `getCurrentConnectDevice()`    | Public   | ✓    | 可能返回null       |
| **WiFi连接** | `setWifiConnectionMode()`      | Public   | ✓    | 支持AP/P2P模式     |
|            | `getWifiConnectionMode()`      | Public   | ✓    | 获取当前连接模式       |
| **消息发送**   | `sendMessageToRiderService()`  | Public   | ✓    | 统一消息发送接口       |
| **导航控制**   | `setNaviMode()`                | Public   | ✓    | 设置本地导航模式       |
|            | `sendNaviModeChange()`         | Public   | ✓    | 发送模式切换请求       |
|            | `sendNaviModeStart()`          | Public   | ✓    | 发送导航开始         |
|            | `sendNaviModeStop()`           | Public   | ✓    | 发送导航停止         |
|            | `sendNaviModeChangeResponse()` | Public   | ✓    | 响应模式切换         |
|            | `needChangeDisplay()`          | Public   | ✓    | 检查显示切换需求       |
| **投屏控制**   | `setMediaProjection()`         | Public   | ✓    | 设置媒体投射权限       |
|            | `requestLockScreenDisplay()`   | Public   | ✓    | 请求锁屏显示         |
|            | `initLockScreenProjection()`   | Public   | ✓    | 初始化锁屏投射        |
|            | `startScreenNavi()`            | Internal | ✓    | 内部调用           |
|            | `stopScreenNavi()`             | Internal | ✓    | 内部调用           |
| **配置管理**   | `getConfigPreferences()`       | Public   | ✓    | 获取配置管理器        |
|            | `deleteConfig()`               | Public   | ✓    | 清除配置           |
|            | `getProductKey()`              | Public   | ✓    | 获取产品密钥         |
|            | `getUuid()`                    | Public   | ✓    | 获取设备UUID       |
|            | `getAutolinkVersion()`         | Public   | ✓    | 获取版本信息         |
| **回调管理**   | `addCallback()`                | Public   | ✓    | 添加事件监听器        |
|            | `removeCallback()`             | Public   | ✓    | 移除事件监听器        |

### 按数据流向分类

| 数据流向       | 消息类型       | 传输通道 | 数据格式               | 频率 |
|------------|------------|------|--------------------|----|
| **SDK→仪表** | 导航信息       | BLE  | NaviInfo           | 高频 |
|            | 通知信息       | BLE | NotificationInfo   | 中频 |
|            | 天气信息       | BLE | WeatherInfo        | 低频 |
|            | 车道信息       | BLE | LaneInfo           | 高频 |
|            | 路口信息       | BLE | NaviCross          | 中频 |
|            | 导航文本       | BLE | NaviText           | 中频 |
|            | 路线信息       | BLE | NaviRoute          | 低频 |
|            | GPS信号      | BLE | GpsSignal          | 中频 |
|            | 导航控制       | BLE  | NaviStart/Stop     | 低频 |
|            | 模式切换       | BLE  | NaviMode           | 低频 |
|            | AutoLink连接 | BLE | AutoLinkConnect    | 低频 |
| **仪表→SDK** | 配置信息       | BLE  | RiderServiceConfig | 低频 |
|            | 模式切换请求     | BLE  | NaviModeChange     | 低频 |
|            | 天气请求       | BLE  | WeatherRequest     | 低频 |
|            | 版本信息       | BLE  | VersionInfo        | 低频 |
|            | WiFi信息     | BLE  | WifiInfo           | 低频 |

### 按使用频率分类

| 使用频率    | API方法                               | 典型场景    |
|---------|-------------------------------------|---------|
| **一次性** | `init()`, `destroy()`               | 应用启动/退出 |
|         | `setWifiConnectionMode()`           | 初始化配置   |
| **低频**  | `startBleScan()`, `connect()`       | 设备连接    |
|         | `setMediaProjection()`              | 投屏授权    |
|         | `deleteConfig()`                    | 重置配置    |
| **中频**  | `sendNaviModeChange()`              | 模式切换    |
|         | `addCallback()`, `removeCallback()` | 生命周期管理  |
| **高频**  | `sendMessageToRiderService()`       | 数据传输    |
|         | `getConnectStatus()`                | 状态查询    |
| **实时**  | 回调事件                                | 事件响应    |

## API 设计原则

1. **单一职责**: 每个API方法只负责一个明确的功能
2. **一致性**: 相似功能的API保持命名和参数风格一致
3. **线程安全**: 所有公开API都是线程安全的
4. **向后兼容**: 保持API的向后兼容性
5. **错误处理**: 通过回调机制处理错误和异常情况
6. **资源管理**: 自动管理连接和资源的生命周期

---
# 新版API 规划
## API 分类概览

基于对现有代码的分析，RiderService SDK 的 API 可以分为以下几个主要类别：

### 1. 生命周期管理 API

**功能描述**: 管理 SDK 的初始化、销毁和状态

| 方法名               | 参数                   | 返回值                    | 描述          |
|-------------------|----------------------|------------------------|-------------|
| `initialize()`    | `RiderServiceConfig` | `suspend Result<Unit>` | 初始化 SDK     |
| `shutdown()`      | -                    | `suspend Result<Unit>` | 销毁 SDK，释放资源 |
| `isInitialized()` | -                    | `Boolean`              | 检查SDK是否已初始化 |
| `getSdkVersion()` | -                    | `String`               | 获取SDK版本     |
| `getApiVersion()` | -                    | `String`               | 获取API版本     |
| `getInstance()`   | -                    | `RiderService`         | 获取单例实例      |

#### 1.1 状态监听

| 属性名            | 类型                        | 描述       |
|----------------|---------------------------|----------|
| `serviceState` | `StateFlow<ServiceState>` | SDK服务状态流 |

### 2. 连接管理 API

**功能描述**: 管理蓝牙和WiFi连接

#### 2.1 设备扫描

| 方法名           | 参数                          | 返回值                               | 描述     |
|---------------|-----------------------------|-----------------------------------|--------|
| `startScan()` | `Duration` (timeout, 默认10秒) | `suspend Result<Flow<BleDevice>>` | 开始蓝牙扫描 |
| `stopScan()`  | -                           | `suspend Result<Unit>`            | 停止蓝牙扫描 |

增加一个持续扫描的API

#### 2.2 设备连接

| 方法名            | 参数          | 返回值                              | 描述       |
|----------------|-------------|----------------------------------|----------|
| `connect()`    | `BleDevice` | `suspend Result<ConnectionInfo>` | 连接指定蓝牙设备 |
| `disconnect()` | -           | `suspend Result<Unit>`           | 断开蓝牙连接   |

提供一个参数为 address 的 connect 方法

#### 2.3 连接状态

| 属性名               | 类型                           | 描述      |
|-------------------|------------------------------|---------|
| `connectionState` | `StateFlow<ConnectionState>` | 连接状态流   |
| `connectedDevice` | `StateFlow<BleDevice?>`      | 当前连接设备流 |

#### 2.4 WiFi配置

| 方法名             | 参数         | 返回值                    | 描述         |
|-----------------|------------|------------------------|------------|
| `setWifiMode()` | `WifiMode` | `suspend Result<Unit>` | 设置WiFi连接模式 |
| `getWifiMode()` | -          | `WifiMode`             | 获取WiFi连接模式 |

#### 2.5 事件监听

| 方法名                          | 参数                   | 返回值    | 描述        |
|------------------------------|----------------------|--------|-----------|
| `addConnectionListener()`    | `ConnectionListener` | `Unit` | 添加连接事件监听器 |
| `removeConnectionListener()` | `ConnectionListener` | `Unit` | 移除连接事件监听器 |

### 3. 消息管理 API

**功能描述**: 向仪表盘发送各种类型的消息

#### 3.1 导航消息

| 方法名                         | 参数                 | 返回值                  | 描述       |
|-----------------------------|--------------------|----------------------|----------|
| `sendNavigationInfo()`      | `NavigationInfo`   | `boolean` | 发送实时导航信息 |
| `sendRouteInfo()`           | `RouteInfo`        | `boolean` | 发送路线信息   |
| `sendLaneInfo()`            | `LaneInfo`         | `boolean` | 发送车道信息   |
| `sendIntersectionInfo()`    | `IntersectionInfo` | `boolean` | 发送路口信息   |
| `sendNavigationText()`      | `NavigationText`   | `boolean` | 发送导航文本   |
| `sendArrivalNotification()` | -                  | `boolean` | 发送到达通知   |

不确定是否需要这样提供，有没有更好的方式

#### 3.2 系统消息

| 方法名                  | 参数                | 返回值                   | 描述      |
|----------------------|-------------------|-----------------------|---------|
| `sendNotification()` | `AppNotification` | `boolean`             | 发送应用通知  |
| `sendWeatherInfo()`  | `WeatherInfo`     | `boolean` | 发送天气信息  |
| `sendGpsStatus()`    | `GpsStatus`       | `boolean` | 发送GPS状态 |

#### 3.3 控制消息

| 方法名                     | 参数             | 返回值                    | 描述             |
|-------------------------|----------------|------------------------|----------------|
| `sendNavigationStart()` | -              | `boolean` | 发送导航开始         |
| `sendNavigationStop()`  | -              | `boolean` | 发送导航停止         |
| `sendAutoLinkConnect()` | `AutoLinkInfo` | `boolean` | 发送AutoLink连接信息 |

#### 3.4 事件监听

| 方法名                       | 参数                | 返回值    | 描述        |
|---------------------------|-------------------|--------|-----------|
| `addMessageListener()`    | `MessageListener` | `Unit` | 添加消息事件监听器 |
| `removeMessageListener()` | `MessageListener` | `Unit` | 移除消息事件监听器 |

### 4. 导航管理 API

**功能描述**: 控制导航模式和状态

#### 4.1 导航模式控制

| 方法名                 | 参数               | 返回值                                | 描述     |
|---------------------|------------------|------------------------------------|--------|
| `setMode()`         | `NavigationMode` | `boolean`                              | 设置导航模式 |
| `switchMode()`      | `NavigationMode` | `suspend Result<ModeChangeResult>` | 切换导航模式 |
| `startNavigation()` | `NavigationMode` | `boolean`             | 开始导航   |
| `stopNavigation()`  | -                | `boolean`             | 停止导航   |

#### 4.2 导航状态

| 属性名               | 类型                           | 描述        |
|-------------------|------------------------------|-----------|
| `navigationState` | `StateFlow<NavigationState>` | 导航状态流     |
| `currentMode`     | `StateFlow<NavigationMode?>` | 当前导航模式流   |
| `isActive`        | `StateFlow<Boolean>`         | 导航是否激活状态流 |

#### 4.3 事件监听

| 方法名                          | 参数                   | 返回值    | 描述        |
|------------------------------|----------------------|--------|-----------|
| `addNavigationListener()`    | `NavigationListener` | `Unit` | 添加导航事件监听器 |
| `removeNavigationListener()` | `NavigationListener` | `Unit` | 移除导航事件监听器 |

#### 4.4 导航模式类型

| 模式            | 描述         |
|---------------|------------|
| `SIMPLE`      | 简易导航（蓝牙导航） |
| `SCREEN`      | 投屏导航       |
| `CRUISE`      | 巡航导航       |
| `MIRROR`      | 镜像导航       |
| `LOCK_SCREEN` | 锁屏导航       |

### 5. 投屏管理 API

**功能描述**: 管理屏幕投射功能

#### 5.1 投屏设置

| 方法名                        | 参数                 | 返回值                                     | 描述       |
|----------------------------|--------------------|-----------------------------------------|----------|
| `setupProjection()`        | `ProjectionConfig` | `suspend Result<Unit>`                  | 设置投屏配置   |
| `requestMediaProjection()` | -                  | `suspend Result<MediaProjectionResult>` | 请求媒体投射权限 |

#### 5.2 投屏控制

| 方法名                  | 参数               | 返回值                                 | 描述   |
|----------------------|------------------|-------------------------------------|------|
| `startProjection()`  | `ProjectionMode` | `suspend Result<ProjectionSession>` | 开始投屏 |
| `stopProjection()`   | -                | `suspend Result<Unit>`              | 停止投屏 |
| `pauseProjection()`  | -                | `suspend Result<Unit>`              | 暂停投屏 |
| `resumeProjection()` | -                | `suspend Result<Unit>`              | 恢复投屏 |

#### 5.3 显示管理

| 方法名                | 参数        | 返回值                    | 描述     |
|--------------------|-----------|------------------------|--------|
| `initDisplay()`    | `Display` | `suspend Result<Unit>` | 初始化显示屏 |
| `releaseDisplay()` | -         | `suspend Result<Unit>` | 释放显示屏  |

#### 5.4 投屏状态

| 属性名                 | 类型                             | 描述       |
|---------------------|--------------------------------|----------|
| `availableDisplays` | `StateFlow<List<Display>>`     | 可用显示屏列表流 |
| `projectionState`   | `StateFlow<ProjectionState>`   | 投屏状态流    |
| `projectionQuality` | `StateFlow<ProjectionQuality>` | 投屏质量流    |

#### 5.5 事件监听

| 方法名                          | 参数                   | 返回值    | 描述        |
|------------------------------|----------------------|--------|-----------|
| `addProjectionListener()`    | `ProjectionListener` | `Unit` | 添加投屏事件监听器 |
| `removeProjectionListener()` | `ProjectionListener` | `Unit` | 移除投屏事件监听器 |

### 6. 配置管理 API

**功能描述**: 管理SDK配置和设备信息

#### 6.1 设备配置

| 方法名                 | 参数 | 返回值                            | 描述     |
|---------------------|----|--------------------------------|--------|
| `getDeviceConfig()` | -  | `suspend Result<DeviceConfig>` | 获取设备配置 |

#### 6.2 设备配置状态

| 属性名            | 类型                         | 描述      |
|----------------|----------------------------|---------|
| `deviceConfig` | `StateFlow<DeviceConfig?>` | 设备配置状态流 |

#### 6.3 用户偏好

| 方法名                    | 参数                | 返回值                               | 描述     |
|------------------------|-------------------|-----------------------------------|--------|
| `getUserPreferences()` | -                 | `suspend Result<UserPreferences>` | 获取用户偏好 |
| `setUserPreferences()` | `UserPreferences` | `suspend Result<Unit>`            | 设置用户偏好 |

#### 6.4 用户偏好状态

| 属性名               | 类型                           | 描述      |
|-------------------|------------------------------|---------|
| `userPreferences` | `StateFlow<UserPreferences>` | 用户偏好状态流 |

#### 6.5 设备信息

| 方法名                | 参数 | 返回值                           | 描述     |
|--------------------|----|-------------------------------|--------|
| `getDeviceInfo()`  | -  | `suspend Result<DeviceInfo>`  | 获取设备信息 |
| `getVersionInfo()` | -  | `suspend Result<VersionInfo>` | 获取版本信息 |

#### 6.6 配置重置

| 方法名                 | 参数 | 返回值                    | 描述      |
|---------------------|----|------------------------|---------|
| `resetToDefaults()` | -  | `suspend Result<Unit>` | 重置为默认配置 |
| `clearAll()`        | -  | `suspend Result<Unit>` | 清除所有配置  |

#### 6.7 事件监听

| 方法名                      | 参数               | 返回值    | 描述        |
|--------------------------|------------------|--------|-----------|
| `addConfigListener()`    | `ConfigListener` | `Unit` | 添加配置事件监听器 |
| `removeConfigListener()` | `ConfigListener` | `Unit` | 移除配置事件监听器 |

### 7. 监听器接口定义

**功能描述**: 各种事件监听器的回调方法

#### 7.1 连接事件监听器

| 监听器                  | 回调方法               | 参数                | 描述     |
|----------------------|--------------------|-------------------|--------|
| `ConnectionListener` | `onStateChanged()` | `ConnectionState` | 连接状态变化 |

#### 7.2 导航事件监听器

| 监听器                  | 回调方法               | 参数                | 描述     |
|----------------------|--------------------|-------------------|--------|
| `NavigationListener` | `onStateChanged()` | `NavigationState` | 导航状态变化 |

#### 7.3 投屏事件监听器

| 监听器                  | 回调方法               | 参数                | 描述     |
|----------------------|--------------------|-------------------|--------|
| `ProjectionListener` | `onStateChanged()` | `ProjectionState` | 投屏状态变化 |

#### 7.4 消息事件监听器

| 监听器               | 回调方法              | 参数                                          | 描述     |
|-------------------|-------------------|---------------------------------------------|--------|
| `MessageListener` | `onMessageSent()` | `String` (messageType), `Boolean` (success) | 消息发送结果 |

#### 7.5 配置事件监听器

| 监听器              | 回调方法                | 参数             | 描述   |
|------------------|---------------------|----------------|------|
| `ConfigListener` | `onConfigChanged()` | `DeviceConfig` | 配置变化 |

### 8. 数据传输对象 (DTO)

#### 8.1 连接相关

| 数据类               | 主要字段                                         | 描述                                                          |
|-------------------|----------------------------------------------|-------------------------------------------------------------|
| `BleDevice`       | `name`, `address`, `rssi`                    | 蓝牙设备信息                                                      |
| `ConnectionInfo`  | `device`, `connectionTime`, `signalStrength` | 连接详细信息                                                      |
| `ConnectionState` | 密封类                                          | 连接状态（Disconnected, Scanning, Connecting, Connected, Failed） |
| `WifiMode`        | 枚举                                           | WiFi模式（AP_CLIENT, P2P, STATION）                             |

#### 8.2 导航相关

| 数据类               | 主要字段                                                                            | 描述                                                |
|-------------------|---------------------------------------------------------------------------------|---------------------------------------------------|
| `NavigationInfo`  | `currentStep`, `remainingDistance`, `nextRoadName`, `turnType`, `estimatedTime` | 实时导航信息                                            |
| `RouteInfo`       | `totalDistance`, `totalTime`, `routePoints`                                     | 路线规划信息                                            |
| `LaneInfo`        | `laneCount`, `currentLane`, `recommendedLanes`                                  | 车道指引信息                                            |
| `NavigationMode`  | 枚举                                                                              | 导航模式（SIMPLE, SCREEN, CRUISE, MIRROR, LOCK_SCREEN） |
| `NavigationState` | 密封类                                                                             | 导航状态（Inactive, Active, Paused, Error）             |

#### 8.3 系统消息

| 数据类               | 主要字段                                               | 描述      |
|-------------------|----------------------------------------------------|---------|
| `AppNotification` | `appName`, `title`, `content`, `timestamp`         | 应用通知信息  |
| `WeatherInfo`     | `temperature`, `condition`, `humidity`, `location` | 天气信息    |
| `GpsStatus`       | `isWeak`, `satelliteCount`, `accuracy`             | GPS信号状态 |

#### 8.4 配置相关

| 数据类               | 主要字段                                                        | 描述     |
|-------------------|-------------------------------------------------------------|--------|
| `DeviceConfig`    | `supportedFeatures`, `displayCapabilities`                  | 设备配置信息 |
| `UserPreferences` | `autoStartNavigation`, `defaultMode`, `enableNotifications` | 用户偏好设置 |
| `DeviceInfo`      | `productKey`, `uuid`, `firmwareVersion`                     | 设备基本信息 |

#### 8.5 投屏相关

| 数据类                 | 主要字段                             | 描述                                      |
|---------------------|----------------------------------|-----------------------------------------|
| `ProjectionMode`    | 枚举                               | 投屏模式（MIRROR, PRESENTATION, LOCK_SCREEN） |
| `ProjectionState`   | 密封类                              | 投屏状态（Inactive, Active, Paused, Error）   |
| `ProjectionSession` | `sessionId`, `startTime`, `mode` | 投屏会话信息                                  |

#### 8.6 错误处理

| 数据类                 | 主要字段                       | 描述                     |
|---------------------|----------------------------|------------------------|
| `Result<T>`         | 密封类                        | 统一结果类型（Success, Error） |
| `RiderServiceError` | `message`, `isRecoverable` | 错误基类                   |
| `ConnectionError`   | 密封类                        | 连接相关错误                 |
| `MessageError`      | 密封类                        | 消息相关错误                 |
| `NavigationError`   | 密封类                        | 导航相关错误                 |
| `ProjectionError`   | 密封类                        | 投屏相关错误                 |

## API 使用统计

| 类别         | 方法数量 | 主要特点             |
|------------|------|------------------|
| **生命周期管理** | 6个   | 基础SDK管理，支持协程     |
| **连接管理**   | 8个   | 蓝牙+WiFi双通道，状态流监听 |
| **消息管理**   | 13个  | 分类消息发送，队列管理      |
| **导航管理**   | 8个   | 多模式导航支持，状态管理     |
| **投屏管理**   | 10个  | 完整投屏方案，显示管理      |
| **配置管理**   | 10个  | 设备信息管理，用户偏好      |
| **监听器接口**  | 5个   | 事件驱动架构，类型安全      |

## 详细API分类表

### 按功能模块分类

| 功能模块       | API方法                  | 返回类型                                | 线程安全 | 备注         |
|------------|------------------------|-------------------------------------|------|------------|
| **生命周期**   | `initialize()`         | `suspend Result<Unit>`              | ✓    | 协程支持的初始化   |
|            | `shutdown()`           | `suspend Result<Unit>`              | ✓    | 协程支持的资源释放  |
|            | `isInitialized()`      | `Boolean`                           | ✓    | 同步状态检查     |
|            | `getSdkVersion()`      | `String`                            | ✓    | 获取SDK版本    |
|            | `getApiVersion()`      | `String`                            | ✓    | 获取API版本    |
| **设备扫描**   | `startScan()`          | `suspend Result<Flow<BleDevice>>`   | ✓    | 返回设备流      |
|            | `stopScan()`           | `suspend Result<Unit>`              | ✓    | 停止扫描       |
| **设备连接**   | `connect()`            | `suspend Result<ConnectionInfo>`    | ✓    | 返回连接详情     |
|            | `disconnect()`         | `suspend Result<Unit>`              | ✓    | 断开连接       |
| **WiFi配置** | `setWifiMode()`        | `suspend Result<Unit>`              | ✓    | 设置WiFi模式   |
|            | `getWifiMode()`        | `WifiMode`                          | ✓    | 获取当前WiFi模式 |
| **导航消息**   | `sendNavigationInfo()` | `suspend Result<Unit>`              | ✓    | 发送导航信息     |
|            | `sendRouteInfo()`      | `suspend Result<Unit>`              | ✓    | 发送路线信息     |
|            | `sendLaneInfo()`       | `suspend Result<Unit>`              | ✓    | 发送车道信息     |
| **系统消息**   | `sendNotification()`   | `suspend Result<Unit>`              | ✓    | 发送通知       |
|            | `sendWeatherInfo()`    | `suspend Result<Unit>`              | ✓    | 发送天气信息     |
|            | `sendGpsStatus()`      | `suspend Result<Unit>`              | ✓    | 发送GPS状态    |
| **导航控制**   | `setMode()`            | `suspend Result<Unit>`              | ✓    | 设置导航模式     |
|            | `switchMode()`         | `suspend Result<ModeChangeResult>`  | ✓    | 切换导航模式     |
|            | `startNavigation()`    | `suspend Result<Unit>`              | ✓    | 开始导航       |
|            | `stopNavigation()`     | `suspend Result<Unit>`              | ✓    | 停止导航       |
| **投屏控制**   | `setupProjection()`    | `suspend Result<Unit>`              | ✓    | 设置投屏配置     |
|            | `startProjection()`    | `suspend Result<ProjectionSession>` | ✓    | 开始投屏       |
|            | `stopProjection()`     | `suspend Result<Unit>`              | ✓    | 停止投屏       |
| **配置管理**   | `getDeviceConfig()`    | `suspend Result<DeviceConfig>`      | ✓    | 获取设备配置     |
|            | `getUserPreferences()` | `suspend Result<UserPreferences>`   | ✓    | 获取用户偏好     |
|            | `setUserPreferences()` | `suspend Result<Unit>`              | ✓    | 设置用户偏好     |
|            | `getDeviceInfo()`      | `suspend Result<DeviceInfo>`        | ✓    | 获取设备信息     |

### 按数据流向分类

| 数据流向       | 消息类型       | 传输通道 | 数据格式                 | 频率 |
|------------|------------|------|----------------------|----|
| **SDK→仪表** | 导航信息       | WiFi | NavigationInfo       | 高频 |
|            | 通知信息       | WiFi | AppNotification      | 中频 |
|            | 天气信息       | WiFi | WeatherInfo          | 低频 |
|            | 车道信息       | WiFi | LaneInfo             | 高频 |
|            | 路口信息       | WiFi | IntersectionInfo     | 中频 |
|            | 导航文本       | WiFi | NavigationText       | 中频 |
|            | 路线信息       | WiFi | RouteInfo            | 低频 |
|            | GPS信号      | WiFi | GpsStatus            | 中频 |
|            | 导航控制       | BLE  | NavigationStart/Stop | 低频 |
|            | 模式切换       | BLE  | NavigationMode       | 低频 |
|            | AutoLink连接 | WiFi | AutoLinkInfo         | 低频 |
| **仪表→SDK** | 配置信息       | BLE  | DeviceConfig         | 低频 |
|            | 模式切换请求     | BLE  | NavigationModeChange | 低频 |
|            | 天气请求       | BLE  | WeatherRequest       | 低频 |
|            | 版本信息       | BLE  | VersionInfo          | 低频 |
|            | WiFi信息     | BLE  | WifiInfo             | 低频 |

### 按使用频率分类

| 使用频率    | API方法                                     | 典型场景    |
|---------|-------------------------------------------|---------|
| **一次性** | `initialize()`, `shutdown()`              | 应用启动/退出 |
|         | `setWifiMode()`                           | 初始化配置   |
|         | `setupProjection()`                       | 投屏配置    |
| **低频**  | `startScan()`, `connect()`                | 设备连接    |
|         | `requestMediaProjection()`                | 投屏授权    |
|         | `resetToDefaults()`                       | 重置配置    |
|         | `setUserPreferences()`                    | 用户设置    |
| **中频**  | `switchMode()`                            | 模式切换    |
|         | `startNavigation()`, `stopNavigation()`   | 导航控制    |
|         | `addXxxListener()`, `removeXxxListener()` | 生命周期管理  |
| **高频**  | `sendNavigationInfo()`, `sendRouteInfo()` | 导航数据传输  |
|         | `sendNotification()`                      | 通知发送    |
| **实时**  | StateFlow状态流                              | 状态监听    |
|         | 监听器回调事件                                   | 事件响应    |
