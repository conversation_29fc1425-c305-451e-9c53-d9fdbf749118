package com.link.riderservice.feature.messaging

import com.google.protobuf.MessageLite
import com.link.riderservice.core.extensions.collection.readInt16BE
import com.link.riderservice.core.extensions.collection.readInt32BE
import com.link.riderservice.core.extensions.collection.readUInt8
import com.link.riderservice.core.extensions.collection.writeInt16BE
import com.link.riderservice.core.utils.logging.logW
import com.link.riderservice.protobuf.RiderProtocol
import java.lang.ref.WeakReference
import java.nio.ByteBuffer
import java.util.concurrent.ConcurrentLinkedDeque

/**
 * <AUTHOR>
 * @date 2022/6/29
 */
internal class MessageManager {
    private val incomingMessages = ConcurrentLinkedDeque<NaviMessage>()
    private val outgoingMessages = ConcurrentLinkedDeque<NaviMessage>()
    private var messageCallbackRef: WeakReference<MessageCallback>? = null

    /**
     * 注册回调
     */
    fun registerCallback(callback: MessageCallback) {
        messageCallbackRef = WeakReference(callback)
    }

    /**
     * 注销回调
     */
    fun unregisterCallback() {
        messageCallbackRef = null
    }

    /**
     * 出队发送数据
     */
    fun dequeueOutgoing(): NaviMessage? {
        return outgoingMessages.poll()
    }

    /**
     * 入队发送数据
     */
    fun queueOutgoing(messageId: Int, messageProto: MessageLite) {
        val dataLength = messageProto.serializedSize
        val byteBuffer = ByteBuffer.allocate(dataLength + MESSAGE_ID_LENGTH)
        val data = byteBuffer.array()
        data.writeInt16BE(messageId)
        System.arraycopy(messageProto.toByteArray(), 0, data, MESSAGE_ID_LENGTH, dataLength)
        messageToFrames(data, data.size)
    }

    /**
     * 入队接收到的数据
     */
    fun enqueueIncoming(buffer: ByteArray, size: Int) {
        decodeFrame(ByteBuffer.wrap(buffer, 0, size))
    }

    private fun enqueueIncoming(message: NaviMessage) {
        incomingMessages.addLast(message)
    }

    /**
     * 清除所有缓存信息
     */
    fun clearMessage() {
        outgoingMessages.clear()
        incomingMessages.clear()
    }

    private fun peekLastIncoming(): NaviMessage? {
        return incomingMessages.peekLast()
    }

    private fun hasIncoming(): Boolean {
        return !incomingMessages.isEmpty()
    }

    private fun dequeueIncoming(): NaviMessage? {
        return incomingMessages.poll()
    }

    private fun hasOutgoing(): Boolean {
        return !outgoingMessages.isEmpty()
    }

    private fun enqueueOutgoing(message: NaviMessage) {
        outgoingMessages.addLast(message)
    }

    private fun messageToFrames(data: ByteArray, dataLength: Int) {
        val message = ByteBuffer.wrap(data)
        var (fragInfo, fragLen) = calculateFragInfoAndLen(dataLength)
        var remaining = dataLength
        var offset = 0
        while (remaining > 0) {
            val payload = ByteArray(fragLen)
            message.position(offset)
            message.get(payload)
            val naviMessage = NaviMessage(
                header = fragInfo,
                messagePayload = payload,
                frameLength = fragLen,
                messageLength = if (fragInfo == RiderProtocol.FragInfo.FRAG_FIRST_VALUE) dataLength else 0
            )
            offset += fragLen
            remaining -= fragLen
            val (newFragInfo, newFragLen) = calculateFragInfoAndLen(remaining)
            fragInfo = newFragInfo
            fragLen = newFragLen
            enqueueOutgoing(naviMessage)
        }
    }

    private fun calculateFragInfoAndLen(dataLength: Int): Pair<Int, Int> {
        return if (dataLength > MAX_PAYLOAD_SIZE) {
            RiderProtocol.FragInfo.FRAG_FIRST_VALUE to MAX_PAYLOAD_SIZE
        } else {
            RiderProtocol.FragInfo.FRAG_UNFRAGMENTED_VALUE to dataLength
        }
    }

    private fun framesToMessage(): ByteArray? {
        var frame = dequeueIncoming()
        if (frame?.header == RiderProtocol.FragInfo.FRAG_UNFRAGMENTED_VALUE) {
            return frame.messagePayload
        }
        var offset = 0
        if (frame != null) {
            val messageData = ByteArray(frame.messageLength)
            while (true) {
                frame?.let { data ->
                    data.messagePayload?.let { payload ->
                        System.arraycopy(payload, 0, messageData, offset, data.frameLength)
                    }
                    offset += data.frameLength
                }
                if (!hasIncoming()) {
                    break
                }
                frame = dequeueIncoming()
            }
            return messageData
        }
        return null
    }

    private fun decodeFrame(buffer: ByteBuffer) {
        val bitField = buffer.array().readUInt8()
        val frameLength = buffer.array().readInt16BE(BITFIELD_LENGTH)
        val messageLength =
            if (bitField == RiderProtocol.FragInfo.FRAG_FIRST_VALUE) buffer.array()
                .readInt32BE(FRAME_LENGTH + BITFIELD_LENGTH) else 0
        val offset = if (messageLength == 0)
            FRAME_LENGTH + BITFIELD_LENGTH
        else
            FRAME_LENGTH + BITFIELD_LENGTH + MESSAGE_LENGTH
        val size = buffer.limit() - offset
        val payload = ByteArray(size)
        buffer.position(offset)
        buffer.get(payload)
        val frame = NaviMessage(
            header = bitField,
            frameLength = frameLength,
            messageLength = messageLength,
            messagePayload = payload,
            originalMessageData = buffer.array()
        )
        val lastFrame = peekLastIncoming()
        val hasIncoming = hasIncoming()
        val valid = isValid(frame, lastFrame, hasIncoming)
        val complete = isComplete(frame, lastFrame, hasIncoming)
        if (!valid) {
            logW(TAG, "invalid frame")
        }
        enqueueIncoming(frame)
        if (complete) {
            val messageData = framesToMessage()
            val type = messageData?.readInt16BE() ?: MESSAGE_TYPE_INVALID
            routeMessage(type, messageData)
        }
    }

    private fun isValid(
        frame: NaviMessage,
        lastFrame: NaviMessage?,
        hasIncoming: Boolean
    ): Boolean {
        return when {
            hasIncoming -> {
                val validFirstOrContinuation =
                    lastFrame?.header == RiderProtocol.FragInfo.FRAG_FIRST_VALUE
                            || lastFrame?.header == RiderProtocol.FragInfo.FRAG_CONTINUATION_VALUE
                val validContinuationOrLast =
                    frame.header == RiderProtocol.FragInfo.FRAG_CONTINUATION_VALUE
                            || frame.header == RiderProtocol.FragInfo.FRAG_LAST_VALUE
                validFirstOrContinuation && validContinuationOrLast
            }

            else -> {
                frame.header == RiderProtocol.FragInfo.FRAG_UNFRAGMENTED_VALUE
                        || frame.header == RiderProtocol.FragInfo.FRAG_FIRST_VALUE
            }
        }
    }

    private fun isComplete(
        frame: NaviMessage,
        lastFrame: NaviMessage?,
        hasIncoming: Boolean
    ): Boolean {
        return when {
            hasIncoming -> {
                val validFirstOrContinuation =
                    lastFrame?.header == RiderProtocol.FragInfo.FRAG_FIRST_VALUE
                            || lastFrame?.header == RiderProtocol.FragInfo.FRAG_CONTINUATION_VALUE
                val validLast = frame.header == RiderProtocol.FragInfo.FRAG_LAST_VALUE
                validFirstOrContinuation && validLast
            }

            else -> {
                frame.header == RiderProtocol.FragInfo.FRAG_UNFRAGMENTED_VALUE
            }
        }
    }

    private fun routeMessage(type: Int, messageData: ByteArray?) {
        if (type == -1) {
            return
        }
        val messagePayload = messageData?.copyOfRange(MESSAGE_ID_LENGTH, messageData.size)
        messageCallbackRef?.get()?.onMessage(type, messagePayload)
    }

    companion object {
        private const val TAG = "MessageManager"
        private const val MAX_PAYLOAD_SIZE = 90
        private const val BITFIELD_LENGTH = 1
        private const val FRAME_LENGTH = 2
        private const val MESSAGE_LENGTH = 4
        private const val MESSAGE_ID_LENGTH = 2
        private const val MESSAGE_TYPE_INVALID = -1

    }
}