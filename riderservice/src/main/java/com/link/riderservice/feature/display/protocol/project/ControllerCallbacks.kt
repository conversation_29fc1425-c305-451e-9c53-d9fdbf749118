package com.link.riderservice.feature.display.protocol.project

/**
 * <AUTHOR>
 * @date 2017/9/19
 * @desc Java perspective of the `IControllerCallbacks` native interface.
 */
/* package private */
internal interface ControllerCallbacks :
    NativeCallback {
    /**
     * Called when an unrecoverable error has been encountered.
     *
     * @param err err
     */
    fun unrecoverableErrorCallback(err: Int)

    /**
     * if auth complete, this callback will be called.
     */
    fun authCompleteCallback()

    /**
     * Called when the other end pings us.
     *
     * @param timestamp timestamp
     * @param bugReport bugReport
     */
    fun pingRequestCallback(
        timestamp: Long,
        bugReport: Boolean
    )

    /**
     * Called when the other end responds to our ping.
     *
     * @param timestamp timestamp
     */
    fun pingResponseCallback(timestamp: Long)

    /**
     * Called when a navigation focus request is received from the phone. You
     * must respond to this by calling Controller::setNavigationFocus() after
     * stopping all native turn by turn guidance.
     *
     * @param type type
     */
    fun navigationFocusCallback(type: Int)

    /**
     * Called when ByeByeRequest is received from phone. After taking necessary
     * steps, car side should send ByeByeResponse.
     *
     * @param reason The reason for the disconnection request.
     */
    fun byeByeRequestCallback(reason: Int)

    /**
     * Called when ByeByeResponse is received from phone. Normally this is a
     * reply for ByeByeRequest message sent from car.
     */
    fun byeByeResponseCallback()

    /**
     * Called when ExitRequest is received from phone. Normally this is a
     * reply for ExitRequest message sent from car.
     */
    fun exitRequestCallback()

    /**
     * Called when the source wishes to acquire audio focus from JNI.
     *
     * @param request     Can be one of AUDIO_FOCUS_GAIN, AUDIO_FOCUS_GAIN_TRANSIENT,
     * AUDIO_FOCUS_GAIN_TRANSIENT_MAY_DUCK, AUDIO_FOCUS_RELEASE.
     * @param unsolicited unsolicited
     */
    fun audioFocusNotificationCallback(
        request: Int,
        unsolicited: Boolean
    )

    /**
     * Called when the source wishes to force landscape.
     *
     * @param force force landscape or not
     */
    fun forceLandscapeRequestCallback(force: Boolean)

    /**
     * Called when the source wishes acquire screenOrientation.
     */
    fun screenOrientationInquire()

    /**
     * Get Car protocol version
     *
     * @param major major
     * @param minor minor
     */
    fun versionResponseCallback(
        major: Short,
        minor: Short
    )

    /**
     * Get car information.
     *
     * @param id          id
     * @param make        make
     * @param model       model
     * @param year        year
     * @param huIc        huIc
     * @param huMake      huMake
     * @param huModel     huModel
     * @param huSwBuild   huSwBuild
     * @param huSwVersion huSwVersion
     * @param huSeries    huSeries
     * @param huMuVersion huMuVersion
     * @param checkSum    checkSum
     */
    fun serviceDiscoveryResponseCallback(
        id: String?,
        make: String?,
        model: String?,
        year: String?,
        huIc: String?,
        huMake: String?,
        huModel: String?,
        huSwBuild: String?,
        huSwVersion: String?,
        huSeries: String?,
        huMuVersion: String?,
        checkSum: Int
    )

    /**
     * Change phone to auto rotation mode.
     *
     * @param autoed autoed
     */
    fun autoRotationRequest(autoed: Boolean)

    /**
     * Get phone screen resolution.
     */
    fun screenResolutionInquire()

    /**
     * Get phone current time.
     */
    fun timeDateInquire()
}