// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef SUNPLUS_AUTOLINK_PROTOCOL_COMMON_H
#define SUNPLUS_AUTOLINK_PROTOCOL_COMMON_H

#include <deque>
#include <set>
#include <utility>
#include <vector>
#include <new>
#include <assert.h>
#include <stdint.h>
#include <sys/types.h>

#include <assert.h>
#include <stddef.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <list>
#include <map>
#include <sys/time.h>
#include <time.h>
#include <errno.h>
#include <limits.h>
#include <unistd.h>
#include <sys/types.h>
#include <arpa/inet.h>
#include <fcntl.h>
#include <linux/tcp.h>
#include <net/if.h>
#include <netdb.h>
#include <netinet/in.h>
#include <sys/ioctl.h>
#include <sys/socket.h>

#include "util/builtin.h"
#include "util/IoBuffer.h"
#include "util/Log.h"
#include "util/Mutex.h"
#include "util/SemaphoreWrapper.h"
#include "util/shared_ptr.h"
#include "Thread.h"
#include "Errors.h"
#include "autolink.pb.h"

using std::deque;
using std::pair;
using std::set;
using std::string;
using std::vector;
using std::list;
using std::map;
using namespace autolink;
/**
 * Warning! If you set this option to 1 then you acknowledge that you will not be
 * able to check for certificates that are not yet valid or potentially expired,
 * which weakens the overall security built into the system. The only reason you
 * should be setting this is if you are not able to provide even a moderate version
 * of current time on your platform.
 */
#define NO_SSL_CERT_DATE_CHECK 0

#define CONTROLLER_SERVICE_ID   0
#define CONTROLLER_CHANNEL_ID   0

#define CHANNEL_PRIORITY_HIGHEST        -128
#define CHANNEL_PRIORITY_LOWEST         127

#define MAX_CHANNELS            256
#define MAX_SERVICES            (MAX_CHANNELS - 1)
#define SENTINEL_SERVICE_ID     MAX_SERVICES

#define FRAME_HEADER_MIN_LENGTH         4

#define BIT_FIELD_OFFSET                1
#define FRAME_LEN_START_OFFSET          2

#define MAX_SLEEPER_CREDIT              256     /* Tuneable */

#define FRAGINFO_SHIFT                  0x0
#define FRAGINFO_MASK                   0x3

#define CHANNEL_CONTROL_SHIFT           0x2
#define CHANNEL_CONTROL_MASK            0x4

#define SET_FRAGINFO(var, val) ((var) |= (((var) & ~FRAGINFO_MASK) | ((val) << FRAGINFO_SHIFT)))
#define FRAG_BITS(var) ((FragInfo)(((var) & FRAGINFO_MASK) >> FRAGINFO_SHIFT))

#define IS_CHANNEL_CONTROL(var) (((var) & CHANNEL_CONTROL_MASK) >> CHANNEL_CONTROL_SHIFT)
#define SET_CHANNEL_CONTROL(var) ((var) |= (1 << CHANNEL_CONTROL_SHIFT))

// Messages are encrypted by default, this allows us to force an unencrypted message.
#define ENCRYPTED_SHIFT                 0x3
#define IS_ENCRYPTED(var)   ((var) & (1 << ENCRYPTED_SHIFT))
#define SET_ENCRYPTED(var, val)  ((var) |= ((!!(val)) << ENCRYPTED_SHIFT))

#define WRITE_BE64(ptr, val) \
do { \
    uint8_t* __ptr = (uint8_t*)(ptr); \
    *__ptr++ = (val) >> 56; \
    *__ptr++ = ((val) & 0x00FF000000000000ULL) >> 48; \
    *__ptr++ = ((val) & 0x0000FF0000000000ULL) >> 40; \
    *__ptr++ = ((val) & 0x000000FF00000000ULL) >> 32; \
    *__ptr++ = ((val) & 0x00000000FF000000ULL) >> 24; \
    *__ptr++ = ((val) & 0x0000000000FF0000ULL) >> 16; \
    *__ptr++ = ((val) & 0x000000000000FF00ULL) >> 8; \
    *__ptr = ((val) & 0x00000000000000FFULL); \
} while (0)

#define WRITE_BE32(ptr, val) \
do { \
    uint8_t* __ptr = (uint8_t*)(ptr); \
    *__ptr++ = (val) >> 24; \
    *__ptr++ = ((val) & 0x00FF0000) >> 16; \
    *__ptr++ = ((val) & 0x0000FF00) >> 8; \
    *__ptr = ((val) & 0x000000FF); \
} while (0)

#define WRITE_BE16(ptr, val) \
do { \
    uint8_t* __ptr = (uint8_t*)(ptr); \
    *__ptr++ = (val) >> 8; \
    *__ptr = (val) & 0x00FF; \
} while (0)

#define WRITE_BE64_PTR_INCR(ptr, val) \
do { \
    WRITE_BE64(ptr, val); \
    ptr += sizeof(val); \
} while (0)

#define WRITE_BE32_PTR_INCR(ptr, val) \
do { \
    WRITE_BE32(ptr, val); \
    ptr += sizeof(val); \
} while (0)

#define WRITE_BE16_PTR_INCR(ptr, val) \
do { \
    WRITE_BE16(ptr, val); \
    ptr += sizeof(val); \
} while (0)

#define READ_BE64(ptr, dest) \
do { \
    uint8_t* __ptr = (uint8_t*)(ptr); \
    (dest) =  (uint64_t)(*__ptr++) << 56; \
    (dest) |= (uint64_t)(*__ptr++) << 48; \
    (dest) |= (uint64_t)(*__ptr++) << 40; \
    (dest) |= (uint64_t)(*__ptr++) << 32; \
    (dest) |= (uint64_t)(*__ptr++) << 24; \
    (dest) |= (uint64_t)(*__ptr++) << 16; \
    (dest) |= (uint64_t)(*__ptr++) << 8; \
    (dest) |= *__ptr; \
} while (0)

#define READ_BE32(ptr, dest) \
do { \
    uint8_t* __ptr = (uint8_t*)(ptr); \
    (dest) = (*__ptr++) << 24; \
    (dest) |= (*__ptr++) << 16; \
    (dest) |= (*__ptr++) << 8; \
    (dest) |= *__ptr; \
} while (0)

#define READ_BE16(ptr, dest) \
do { \
    uint8_t* __ptr = (uint8_t*)(ptr); \
    (dest) = (*__ptr++) << 8; \
    (dest) |= *__ptr; \
} while (0)

#define PARSE_PROTO(proto, ptr, len) ((proto).ParseFromArray((ptr), (len)) ? true : \
        (LOG("Failed to parse proto at %s:%d", __FILE__, __LINE__) && false))

struct Frame {
    uint8_t channelId;
    uint8_t bitField;
    uint16_t frameLength;
    uint32_t messageLength;
    void *payload;
    shared_ptr<IoBuffer> original;
};

typedef struct FileInfo_t {
    string name;
    int32_t versionX;
    int32_t versionY;
    int32_t versionZ;
    int32_t size;
    string module;
} FileInfo;

typedef struct CheckResult_t {
    int32_t moduleId;
    int32_t failCode;
    int32_t value;
} CheckResult;

#endif // SUNPLUS_AUTOLINK_PROTOCOL_COMMON_H
