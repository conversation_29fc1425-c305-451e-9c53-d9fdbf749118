package com.link.riderservice.api

import com.link.riderservice.core.utils.logging.LogLevel
import com.link.riderservice.core.utils.logging.LogManager

/**
 * RiderService 日志管理API
 * 提供对外的日志控制接口
 */
object LogApi {
    
    // ==================== 全局日志控制 ====================
    
    /**
     * 启用日志输出
     */
    @JvmStatic
    fun enableLog() {
        LogManager.enableLog()
    }
    
    /**
     * 禁用日志输出
     */
    @JvmStatic
    fun disableLog() {
        LogManager.disableLog()
    }
    
    /**
     * 检查日志是否启用
     * @return true表示启用，false表示禁用
     */
    @JvmStatic
    fun isLogEnabled(): Boolean {
        return LogManager.isLogEnabled()
    }
    
    /**
     * 设置全局日志级别
     * @param level 日志级别，可选值：
     *              - VERBOSE: 显示所有日志
     *              - DEBUG: 显示DEBUG及以上级别
     *              - INFO: 显示INFO及以上级别
     *              - WARN: 显示WARN及以上级别
     *              - ERROR: 只显示ERROR级别
     *              - ASSERT: 只显示ASSERT级别
     */
    @JvmStatic
    fun setLogLevel(level: LogLevel) {
        LogManager.setLogLevel(level)
    }
    
    /**
     * 获取当前全局日志级别
     * @return 当前的日志级别
     */
    @JvmStatic
    fun getLogLevel(): LogLevel {
        return LogManager.getLogLevel()
    }
    
    // ==================== 标签特定控制 ====================
    
    /**
     * 为特定标签启用日志
     * @param tag 标签名
     */
    @JvmStatic
    fun enableLogForTag(tag: String) {
        LogManager.enableLogForTag(tag)
    }
    
    /**
     * 为特定标签禁用日志
     * @param tag 标签名
     */
    @JvmStatic
    fun disableLogForTag(tag: String) {
        LogManager.disableLogForTag(tag)
    }
    
    /**
     * 为特定标签设置日志级别
     * @param tag 标签名
     * @param level 日志级别
     */
    @JvmStatic
    fun setLogLevelForTag(tag: String, level: LogLevel) {
        LogManager.setLogLevelForTag(tag, level)
    }
    
    /**
     * 移除特定标签的设置（恢复使用全局设置）
     * @param tag 标签名
     */
    @JvmStatic
    fun removeTagSettings(tag: String) {
        LogManager.removeTagSettings(tag)
    }
    
    /**
     * 清除所有标签特定设置
     */
    @JvmStatic
    fun clearAllTagSettings() {
        LogManager.clearAllTagSettings()
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 启用详细模式（显示所有日志）
     */
    @JvmStatic
    fun enableVerboseMode() {
        LogManager.enableLog()
        LogManager.setLogLevel(LogLevel.VERBOSE)
    }
    
    /**
     * 启用调试模式（显示DEBUG及以上级别）
     */
    @JvmStatic
    fun enableDebugMode() {
        LogManager.enableLog()
        LogManager.setLogLevel(LogLevel.DEBUG)
    }
    
    /**
     * 启用生产模式（只显示WARN和ERROR）
     */
    @JvmStatic
    fun enableProductionMode() {
        LogManager.enableLog()
        LogManager.setLogLevel(LogLevel.WARN)
    }
    
    /**
     * 启用安静模式（只显示ERROR）
     */
    @JvmStatic
    fun enableQuietMode() {
        LogManager.enableLog()
        LogManager.setLogLevel(LogLevel.ERROR)
    }
} 