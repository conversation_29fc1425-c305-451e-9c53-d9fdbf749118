package com.link.riderservice.feature.connection.transport.tcp

/**
 * TCP连接相关常量定义
 * 统一管理所有TCP连接相关的常量，避免重复定义
 * 
 * <AUTHOR> Assistant
 * @date 2024
 */
object TcpConnectionConstants {
    
    // 端口配置
    const val DEFAULT_LISTEN_PORT = 30512
    
    // 超时配置
    const val CONNECTION_TIMEOUT = 30000 // 30秒连接超时
    const val SO_TIMEOUT = 30000 // 30秒读取超时
    const val ACCEPT_TIMEOUT = 30000 // 30秒接受连接超时
    
    // 重试配置
    const val MAX_RETRY_COUNT = 3
    const val RETRY_DELAY = 1000L // 1秒重试延迟
    
    // Socket配置
    const val TCP_NO_DELAY = true
    const val KEEP_ALIVE = true
    const val REUSE_ADDRESS = true
    
    // 缓冲区配置
    const val DEFAULT_BUFFER_SIZE = 8192
    const val MAX_BUFFER_SIZE = 65536
    const val TAG_TCP_TRANSPORT = "TcpTransport"
}
