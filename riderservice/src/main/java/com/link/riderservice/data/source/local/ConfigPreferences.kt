package com.link.riderservice.data.source.local

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit

/**
 * 配置自动连接的 SharedPreferences 管理类
 * 单例模式，避免重复调用 getSharedPreferences
 */
class ConfigPreferences private constructor(context: Context) {

    companion object {
        private const val PREF_NAME = "config_auto_connection"
        private const val KEY_BLE_NAME = "ble_name"
        private const val KEY_BLE_ADDRESS = "ble_address"
        private const val KEY_WIFI_ADDRESS = "wifi_address"
        private const val KEY_WIFI_PORT = "wifi_port"
        private const val KEY_SCAN_BLE_ADDRESS = "scan_ble_address"
        private const val KEY_AP_SSID = "ap_ssid"
        private const val KEY_AP_PASSWORD = "ap_password"

        @Volatile
        private var INSTANCE: ConfigPreferences? = null

        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): ConfigPreferences {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ConfigPreferences(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)

    /**
     * 保存 BLE 设备名称
     */
    fun setBleName(name: String) {
        sharedPreferences.edit {
            putString(KEY_BLE_NAME, name)
        }
    }

    /**
     * 获取 BLE 设备名称
     */
    fun getBleName(): String? {
        return sharedPreferences.getString(KEY_BLE_NAME, null)
    }

    /**
     * 保存 BLE 设备地址
     */
    fun setBleAddress(address: String) {
        sharedPreferences.edit {
            putString(KEY_BLE_ADDRESS, address)
        }
    }

    /**
     * 获取 BLE 设备地址
     */
    fun getBleAddress(): String? {
        return sharedPreferences.getString(KEY_BLE_ADDRESS, null)
    }

    /**
     * 保存 WiFi 地址
     */
    fun setWifiAddress(address: String) {
        sharedPreferences.edit {
            putString(KEY_WIFI_ADDRESS, address)
        }
    }

    /**
     * 获取 WiFi 地址
     */
    fun getWifiAddress(): String? {
        return sharedPreferences.getString(KEY_WIFI_ADDRESS, null)
    }

    /**
     * 保存 WiFi 端口
     */
    fun setWifiPort(port: Int) {
        sharedPreferences.edit {
            putInt(KEY_WIFI_PORT, port)
        }
    }

    /**
     * 获取 WiFi 端口
     */
    fun getWifiPort(): Int {
        return sharedPreferences.getInt(KEY_WIFI_PORT, 0)
    }

    /**
     * 保存扫描 BLE 设备地址
     */
    fun setScanBleAddress(address: String) {
        sharedPreferences.edit {
            putString(KEY_SCAN_BLE_ADDRESS, address)
        }
    }

    /**
     * 获取扫描 BLE 设备地址
     */
    fun getScanBleAddress(): String? {
        return sharedPreferences.getString(KEY_SCAN_BLE_ADDRESS, null)
    }

    /**
     * 检查是否包含某个键
     */
    fun contains(key: String): Boolean {
        return sharedPreferences.contains(key)
    }

    /**
     * 检查是否包含扫描 BLE 地址
     */
    fun containsScanBleAddress(): Boolean {
        return sharedPreferences.contains(KEY_SCAN_BLE_ADDRESS)
    }

    /**
     * 检查是否包含 BLE 地址
     */
    fun containsBleAddress(): Boolean {
        return sharedPreferences.contains(KEY_BLE_ADDRESS)
    }

    /**
     * 删除扫描 BLE 地址
     */
    fun removeScanBleAddress() {
        sharedPreferences.edit {
            remove(KEY_SCAN_BLE_ADDRESS)
        }
    }

    /**
     * 保存AP模式SSID
     */
    fun setApSsid(ssid: String) {
        sharedPreferences.edit {
            putString(KEY_AP_SSID, ssid)
        }
    }

    /**
     * 获取AP模式SSID
     */
    fun getApSsid(): String? {
        return sharedPreferences.getString(KEY_AP_SSID, null)
    }

    /**
     * 保存AP模式密码
     */
    fun setApPassword(password: String) {
        sharedPreferences.edit {
            putString(KEY_AP_PASSWORD, password)
        }
    }

    /**
     * 获取AP模式密码
     */
    fun getApPassword(): String? {
        return sharedPreferences.getString(KEY_AP_PASSWORD, null)
    }

    /**
     * 清除所有配置
     */
    fun clearAllConfig() {
        sharedPreferences.edit {
            remove(KEY_BLE_NAME)
            remove(KEY_BLE_ADDRESS)
            remove(KEY_WIFI_ADDRESS)
            remove(KEY_WIFI_PORT)
            remove(KEY_AP_SSID)
            remove(KEY_AP_PASSWORD)
        }
    }

    /**
     * 获取原始 SharedPreferences 对象（用于兼容现有代码）
     */
    internal fun getSharedPreferences(): SharedPreferences {
        return sharedPreferences
    }
}