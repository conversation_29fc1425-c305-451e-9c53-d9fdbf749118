package com.link.riderservice.core.utils.system

import android.os.Process
import com.link.riderservice.core.utils.logging.logE

internal object CrashHandler : Thread.UncaughtExceptionHandler {
    private const val TAG = "CrashHandler"
    private var defaultCrashHandler: Thread.UncaughtExceptionHandler? = null
    
    fun init() {
        defaultCrashHandler = Thread.getDefaultUncaughtExceptionHandler()
        Thread.setDefaultUncaughtExceptionHandler(this)
    }

    override fun uncaughtException(thread: Thread, throwable: Throwable) {
        logE(TAG, "uncaughtException", throwable)
        if (defaultCrashHandler != null) {
            defaultCrashHandler?.uncaughtException(thread, throwable)
        } else {
            Process.killProcess(Process.myPid())
        }
    }
}