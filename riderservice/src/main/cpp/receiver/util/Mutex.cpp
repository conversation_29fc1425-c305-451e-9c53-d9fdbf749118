// Copyright 2014 Google Inc. All Rights Reserved.

#include "Mutex.h"

Mutex::Mutex() {
    pthread_mutex_init(&mMutex, NULL);
}

Mutex::~Mutex() {
    pthread_mutex_destroy(&mMutex);
}

void Mutex::lock() {
    pthread_mutex_lock(&mMutex);
}

void Mutex::unlock() {
    pthread_mutex_unlock(&mMutex);
}

Autolock::Autolock(Mutex *mutex) : mMutex(mutex) {
    mutex->lock();
}

Autolock::~Autolock() {
    mMutex->unlock();
}
