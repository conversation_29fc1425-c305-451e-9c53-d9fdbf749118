//
// Created by w.feng on 2020/6/1.
//
#include "RTPSender.h"
#include "util/Timers.h"
#include "util./avc_utils.h"

RTPSender::RTPSender(
        const shared_ptr<NetworkSession> &netSession)
        : mNetSession(netSession),
          mRTPMode(TRANSPORT_UNDEFINED),
          mRTCPMode(TRANSPORT_UNDEFINED),
          mRTPSessionID(0),
          mRTCPSessionID(0),
          mRTPConnected(false),
          mRTCPConnected(false),
          mRTPSeqNo(0),
          mHistorySize(0) {
}


RTPSender::~RTPSender() {
    if (mRTCPSessionID != 0) {
        mNetSession->destroySession(mRTCPSessionID);
        mRTCPSessionID = 0;
    }
    if (mRTPSessionID != 0) {
        mNetSession->destroySession(mRTPSessionID);
        mRTPSessionID = 0;
    }
}

int32_t RTPBase::PickRandomRTPPort() {
    static const size_t kRange = (65534 - 1024) / 2;
    return (int32_t) (((float) (kRange + 1) * rand()) / RAND_MAX) * 2 + 1024;
}


status_t RTPSender::initAsync(
        const char *remoteHost,
        int32_t remoteRTPPort,
        TransportMode rtpMode,
        int32_t remoteRTCPPort,
        TransportMode rtcpMode,
        int32_t *outLocalRTPPort) {
    if (mRTPMode != TRANSPORT_UNDEFINED
        || rtpMode == TRANSPORT_UNDEFINED
        || rtpMode == TRANSPORT_NONE
        || rtcpMode == TRANSPORT_UNDEFINED) {
        return INVALID_OPERATION;
    }

    int32_t localRTPPort;
    for (;;) {
        localRTPPort = PickRandomRTPPort();
        status_t err = OK;
        if (rtpMode == TRANSPORT_UDP) {
            err = mNetSession->createUDPSession(
                    localRTPPort,
                    remoteHost,
                    remoteRTPPort,
                    &mRTPSessionID);
        }

        if (err != OK) {
            continue;
        }

        if (remoteRTCPPort < 0) {
            break;
        }

        mNetSession->destroySession(mRTPSessionID);
        mRTPSessionID = 0;
    }

    if (rtpMode == TRANSPORT_UDP) {
        mRTPConnected = true;
    }
    mRTPMode = rtpMode;
    mRTCPMode = rtcpMode;
    *outLocalRTPPort = localRTPPort;
    return OK;
}

status_t RTPSender::queueBuffer(
        void *data,
        size_t len,
        uint8_t packType,
        PacketizationMode mode) {
    status_t err = OK;
    switch (mode) {
        case PACKETIZATION_NONE:
        case PACKETIZATION_TRANSPORT_STREAM:
            err = queueTSPackets(data, len, packType);
            break;
        case PACKETIZATION_H264:
            err = queueAVCBuffer(data, len, packType);
            break;
        default:
            break;
    }
    return err;
}

status_t RTPSender::queueTSPackets(void *data, size_t len, uint8_t packetType) {
    static const size_t kFullRTPPacketSize = 7 * 188;
    size_t srcOffset = 0;
    int64_t nowUs = systemTime(SYSTEM_TIME_MONOTONIC) / 1000ll;
    uint32_t rtpTime = (nowUs * 9ll) / 100ll;
    while (srcOffset < len) {
        shared_ptr<IoBuffer> udpPacket = new IoBuffer(12 + kMaxNumTSPacketsPerRTPPacket * 188);
        udpPacket->setInt32Data(mRTPSeqNo);

        auto *rtp = udpPacket->data();
        rtp[0] = 0x80;
        rtp[1] = packetType;
        rtp[2] = (mRTPSeqNo >> 8) & 0xff;
        rtp[3] = mRTPSeqNo & 0xff;
        ++mRTPSeqNo;
        rtp[4] = rtpTime >> 24u;
        rtp[5] = (rtpTime >> 16) & 0xff;
        rtp[6] = (rtpTime >> 8) & 0xff;
        rtp[7] = rtpTime & 0xff;

        rtp[8] = kSourceID >> 24;
        rtp[9] = (kSourceID >> 16) & 0xff;
        rtp[10] = (kSourceID >> 8) & 0xff;
        rtp[11] = kSourceID & 0xff;
        size_t rtpPacketSize = len - srcOffset;
        if (rtpPacketSize > kFullRTPPacketSize) {
            rtpPacketSize = kFullRTPPacketSize;
        }
        memcpy(&rtp[12], (uint8_t *) data + srcOffset, rtpPacketSize);
        udpPacket->setStartOffset(0);
        udpPacket->setEndOffset(12 + rtpPacketSize);
        srcOffset += rtpPacketSize;
        status_t err = sendRTPPacket(udpPacket, false);
        if (err != OK) {
            return err;
        }
    }
    return OK;
}

status_t RTPSender::queueAVCBuffer(void *_data, size_t len, uint8_t packetType) {
    int64_t nowUs = systemTime(SYSTEM_TIME_MONOTONIC) / 1000ll;
    uint32_t rtpTime = (nowUs * 9ll) / 100ll;
    list<shared_ptr<IoBuffer>> packets;

    shared_ptr<IoBuffer> out = new IoBuffer(kMaxUDPPacketSize);
    size_t outBytesUsed = 12;
    const auto *data = (const uint8_t *) _data;
    size_t size = len;
    const uint8_t *nalStart;
    size_t nalSize;
    while (getNextNALUnit(&data, &size, &nalStart, &nalSize, true) == OK) {
        size_t bytesNeeded = nalSize + 2;
        if (outBytesUsed == 12) {
            ++bytesNeeded;
        }
        if (outBytesUsed + bytesNeeded > out->capacity()) {
            bool emitSingleNALPacket = false;
            if (outBytesUsed == 12 && outBytesUsed + nalSize <= out->capacity()) {
                // We haven't emitted anything into the current packet yet and
                // this NAL unit fits into a single-NAL-unit-packet while
                // it wouldn't have fit as part of a STAP-A packet.

                memcpy((uint8_t *) out->raw() + outBytesUsed, nalStart, nalSize);
                outBytesUsed += nalSize;
                emitSingleNALPacket = true;
            }

            if (outBytesUsed > 12) {
                out->setStartOffset(0);
                out->setEndOffset(outBytesUsed);
                packets.push_back(out);
                out = new IoBuffer(kMaxUDPPacketSize);
                outBytesUsed = 12;
            }

            if (emitSingleNALPacket) {
                continue;
            }
        }

        if (outBytesUsed + bytesNeeded <= out->capacity()) {
            uint8_t *dst = (uint8_t *) out->raw() + outBytesUsed;
            if (outBytesUsed == 12) {
                *dst++ = 24; // STAP-A header
            }

            *dst++ = (nalSize >> 8) & 0xff;
            *dst++ = nalSize & 0xff;
            memcpy(dst, nalStart, nalSize);
            outBytesUsed += bytesNeeded;
            continue;
        }

        // This single NAL unit does not fit into a single RTP packet,
        // we need to emit an FU-A.

        uint8_t nalType = nalStart[0] & 0x1f;
        uint8_t nri = (nalStart[0] >> 5) & 3;
        size_t srcOffset = 1;
        while (srcOffset < nalSize) {
            size_t copy = out->capacity() - outBytesUsed - 2;
            if (copy > nalSize - srcOffset) {
                copy = nalSize - srcOffset;
            }

            uint8_t *dst = (uint8_t *) out->raw() + outBytesUsed;
            dst[0] = (nri << 5) | 28;
            dst[1] = nalType;
            if (srcOffset == 1) {
                dst[1] |= 0x80;
            }
            if (srcOffset + copy == nalSize) {
                dst[1] |= 0x40;
            }
            memcpy(&dst[2], nalStart + srcOffset, copy);
            srcOffset += copy;
            out->setStartOffset(0);
            out->setEndOffset(outBytesUsed + copy + 2);
            packets.push_back(out);
            out = new IoBuffer(kMaxUDPPacketSize);
            outBytesUsed = 12;
        }

    }
    if (outBytesUsed > 12) {
        out->setStartOffset(0);
        out->setEndOffset(outBytesUsed);
        packets.push_back(out);
    }

    while (!packets.empty()) {
        shared_ptr<IoBuffer> out = *packets.begin();
        packets.erase(packets.begin());
        out->setInt32Data(mRTPSeqNo);
        bool last = packets.empty();
        auto *dst = (uint8_t *) out->raw();
        dst[0] = 0x80;
        dst[1] = packetType;
        if (last) {
            dst[1] |= 1 << 7;
        }
        dst[2] = (mRTPSeqNo >> 8) & 0xff;
        dst[3] = mRTPSeqNo & 0xff;
        ++mRTPSeqNo;
        dst[4] = rtpTime >> 24;
        dst[5] = (rtpTime >> 16) & 0xff;
        dst[6] = (rtpTime >> 8) & 0xff;
        dst[7] = rtpTime & 0xff;

        dst[8] = kSourceID >> 24;
        dst[9] = (kSourceID >> 16) & 0xff;
        dst[10] = (kSourceID >> 8) & 0xff;
        dst[11] = kSourceID & 0xff;

        status_t err = sendRTPPacket(out, false);
        if (err != OK) {
            return err;
        }
    }
    return OK;
}

status_t RTPSender::sendRTPPacket(
        const shared_ptr<IoBuffer> &packet,
        bool storeInHistory,
        bool timeValid,
        int64_t timeUs) {
    if (!mRTPConnected) {
        return INVALID_OPERATION;
    }
    status_t err = mNetSession->sendRequest(
            mRTPSessionID,
            packet,
            timeValid,
            timeUs);
    if (err != OK) {
        return err;
    }
    return OK;
}

