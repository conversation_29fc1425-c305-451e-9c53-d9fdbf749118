package com.link.riderservice.data.authorization.domain.repository

import com.link.riderservice.data.authorization.domain.entity.Activate
import com.link.riderservice.data.authorization.domain.entity.CheckInfo

internal interface AuthorizeRepository {
    suspend fun requestActivateStatus(
        key: String,
        mac: String,
        time: String,
        sign: String
    ): Result<Activate>

    suspend fun requestCheckStatus(
        key: String,
        mac: String,
        uuid: String,
        time: String,
        licenseSign: String,
        sign: String
    ): Result<CheckInfo>
}