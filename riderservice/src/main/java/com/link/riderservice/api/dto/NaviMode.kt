package com.link.riderservice.api.dto

/**
 * 导航模式
 */
sealed class NaviMode {
    object NoNavi : NaviMode()


    object Default : NaviMode()

    /**
     * [SimpleNavi] 简易导航，用于蓝牙导航
     */
    object SimpleNavi : NaviMode()

    /**
     * [ScreenNavi] 投屏导航，用于竖屏导航
     */
    object ScreenNavi : NaviMode()

    /**
     * [CruiseNAVI] 投屏导航，用于横屏导航
     */
    object CruiseNAVI : NaviMode()

    /**
     * [MirrorNAVI] 镜像
     */
    object MirrorNAVI : NaviMode()

    object LockScreenNavi : NaviMode()
}