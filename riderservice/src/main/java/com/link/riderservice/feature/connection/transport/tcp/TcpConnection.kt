package com.link.riderservice.feature.connection.transport.tcp

import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.logging.logE
import com.link.riderservice.feature.connection.transport.DataConnection
import java.io.IOException
import java.net.BindException
import java.net.InetSocketAddress
import java.net.ServerSocket
import java.util.concurrent.atomic.AtomicBoolean

class TcpConnection : DataConnection() {
    private var serverSocket: ServerSocket? = null
    private var isStarted = AtomicBoolean(false)


    @Throws(IOException::class)
    override fun onStart() {
        if (serverSocket == null || serverSocket?.isClosed == true) {
            serverSocket = ServerSocket().apply {
                reuseAddress = true
            }
            try {
                serverSocket?.bind(InetSocketAddress(LISTEN_PORT))
            } catch (exception: BindException) {
                logE(TAG, "${exception.message}")
            }
        }
        ConnectThread("TCP Server").start()
    }

    inner class ConnectThread(name: String) : Thread(name) {
        override fun run() {
            if (!isStarted.get()) {
                try {
                    logD(TAG, "Listening on")
                    isStarted.set(true)
                    val clientSocket = serverSocket?.accept()
                    logD("connect analysis:", "tcp connect end::")
                    logD(TAG, "accept ${clientSocket?.inetAddress}")
                    clientSocket?.let {
                        val tcpTransport = TcpTransport(clientSocket)
                        eventCallback?.onConnected(tcpTransport)
                    }
                } catch (exception: IOException) {
                    logE(TAG, "Can't listen to socket", exception)
                    eventCallback?.onDisconnected()
                }
            }
        }
    }

    override fun onShutdown() {
        isStarted.set(false)
        try {
            serverSocket?.close()
        } catch (exception: IOException) {
            logE(TAG, "Can't close server socket", exception)
        }
        serverSocket = null
    }

    companion object {
        private const val LISTEN_PORT = 30512
        private const val TAG = "TcpConnection"
    }

}