# RiderService SDK 完整API列表

**版本**: 2.2 | **状态**: 完整API清单 | **类型**: API参考文档

---

## 目录

- [Builder API](#builder-api)
- [核心服务API](#核心服务api)
- [连接管理API](#连接管理api)
- [消息管理API](#消息管理api)
- [导航管理API](#导航管理api)
- [投屏管理API](#投屏管理api)
- [配置管理API](#配置管理api)
- [监听器API](#监听器api)
- [数据类型API](#数据类型api)
- [枚举类型API](#枚举类型api)

---

## Builder API

### RiderServiceBuilder - 主构建器

```kotlin
class RiderServiceBuilder {
    companion object {
        fun newBuilder(): RiderServiceBuilder
        fun createDefault(application: Application): RiderService
    }
    
    // === 基础配置 ===
    fun application(application: Application): RiderServiceBuilder
    fun debugMode(enabled: Boolean): RiderServiceBuilder
    fun logLevel(level: LogLevel): RiderServiceBuilder
    
    // === 模块配置 ===
    fun connection(block: ConnectionConfigBuilder.() -> Unit): RiderServiceBuilder
    fun messaging(block: MessagingConfigBuilder.() -> Unit): RiderServiceBuilder
    fun navigation(block: NavigationConfigBuilder.() -> Unit): RiderServiceBuilder
    fun projection(block: ProjectionConfigBuilder.() -> Unit): RiderServiceBuilder
    fun userPreferences(block: UserPreferencesBuilder.() -> Unit): RiderServiceBuilder
    
    // === 构建 ===
    fun build(): RiderService
}
```

### ConnectionConfigBuilder - 连接配置构建器

```kotlin
class ConnectionConfigBuilder {
    fun wifiMode(mode: WifiConnectionMode): ConnectionConfigBuilder
    fun wifiAutoConnect(enabled: Boolean): ConnectionConfigBuilder
    fun wifiTimeout(timeout: Duration): ConnectionConfigBuilder
    fun bleTimeout(scanTimeout: Duration, connectionTimeout: Duration): ConnectionConfigBuilder
    fun autoReconnect(enabled: Boolean): ConnectionConfigBuilder
    fun retryPolicy(policy: RetryPolicy): ConnectionConfigBuilder
    fun enableMetrics(enabled: Boolean): ConnectionConfigBuilder
}
```

### MessagingConfigBuilder - 消息配置构建器

```kotlin
class MessagingConfigBuilder {
    fun queueSize(size: Int): MessagingConfigBuilder
    fun messageTimeout(timeout: Duration): MessagingConfigBuilder
    fun enablePersistence(enabled: Boolean): MessagingConfigBuilder
    fun batchSending(enabled: Boolean, batchSize: Int = 10): MessagingConfigBuilder
    fun enablePriorityQueue(enabled: Boolean): MessagingConfigBuilder
    fun enableCompression(enabled: Boolean): MessagingConfigBuilder
}
```

### NavigationConfigBuilder - 导航配置构建器

```kotlin
class NavigationConfigBuilder {
    fun defaultMode(mode: NaviMode): NavigationConfigBuilder
    fun autoStart(enabled: Boolean): NavigationConfigBuilder
    fun allowModeSwitch(enabled: Boolean): NavigationConfigBuilder
    fun modeSwitchTimeout(timeout: Duration): NavigationConfigBuilder
    fun updateInterval(interval: Duration): NavigationConfigBuilder
    fun enableRouteOptimization(enabled: Boolean): NavigationConfigBuilder
    fun enableVoiceGuidance(enabled: Boolean): NavigationConfigBuilder
    fun enableLaneGuidance(enabled: Boolean): NavigationConfigBuilder
    fun enableSpeedLimit(enabled: Boolean): NavigationConfigBuilder
    fun enableTrafficInfo(enabled: Boolean): NavigationConfigBuilder
    fun mapOrientation(orientation: MapOrientation): NavigationConfigBuilder
    fun zoomLevel(level: Int): NavigationConfigBuilder
    fun enableNightMode(enabled: Boolean): NavigationConfigBuilder
}
```

### ProjectionConfigBuilder - 投屏配置构建器

```kotlin
class ProjectionConfigBuilder {
    fun autoProjection(enabled: Boolean): ProjectionConfigBuilder
    fun quality(quality: ProjectionQuality): ProjectionConfigBuilder
    fun frameRate(rate: Int): ProjectionConfigBuilder
    fun resolution(res: Resolution): ProjectionConfigBuilder
    fun enableMirror(enabled: Boolean): ProjectionConfigBuilder
    fun mirrorOrientation(orientation: MirrorOrientation): ProjectionConfigBuilder
    fun enableAudioMirror(enabled: Boolean): ProjectionConfigBuilder
    fun enableHardwareAcceleration(enabled: Boolean): ProjectionConfigBuilder
    fun bufferSize(size: Int): ProjectionConfigBuilder
    fun maxBitrate(bitrate: Int): ProjectionConfigBuilder
    fun enableAdaptiveBitrate(enabled: Boolean): ProjectionConfigBuilder
    fun displaySettings(brightness: Float, contrast: Float, saturation: Float): ProjectionConfigBuilder
    fun touchSettings(enableTouch: Boolean, enableGesture: Boolean, sensitivity: Float): ProjectionConfigBuilder
}
```

### UserPreferencesBuilder - 用户偏好配置构建器

```kotlin
class UserPreferencesBuilder {
    fun autoStartNavigation(enabled: Boolean): UserPreferencesBuilder
    fun defaultNavigationMode(mode: NaviMode): UserPreferencesBuilder
    fun preferredMapStyle(style: MapStyle): UserPreferencesBuilder
    fun notifications(enabled: Boolean, types: Set<NotificationType>): UserPreferencesBuilder
    fun notificationSettings(sound: Boolean, vibration: Boolean): UserPreferencesBuilder
    fun displaySettings(theme: Theme, fontSize: FontSize, language: String): UserPreferencesBuilder
    fun units(units: Units): UserPreferencesBuilder
    fun connectionSettings(wifiMode: WifiConnectionMode, autoConnect: Boolean, timeout: Duration): UserPreferencesBuilder
    fun privacySettings(analytics: Boolean, crashReporting: Boolean, locationSharing: Boolean): UserPreferencesBuilder
    fun performanceSettings(powerSaving: Boolean, dataUsage: DataUsageLevel, cacheSize: Int): UserPreferencesBuilder
}
```

---

## 核心服务API

### RiderService - 主服务接口

```kotlin
interface RiderService {
    // === 管理器访问 ===
    val connection: ConnectionManager
    val messaging: MessagingManager
    val navigation: NavigationManager
    val projection: ProjectionManager
    val config: ConfigManager
    
    // === 生命周期管理 ===
    fun destroy()
    fun isInitialized(): Boolean
    
    // === 全局状态 ===
    val serviceState: StateFlow<ServiceState>
    
    // === 版本信息 ===
    fun getSdkVersion(): String
    fun getApiVersion(): String
    
    companion object {
        @Deprecated("Use RiderServiceBuilder instead")
        fun getInstance(): RiderService
    }
}
```

---

## 连接管理API

### ConnectionManager - 连接管理接口

```kotlin
interface ConnectionManager {
    // === 设备扫描 ===
    suspend fun startBleScan(timeoutMs: Long = 10000): Result<List<BleDevice>>
    fun stopBleScan()
    fun isScanning(): Boolean
    
    // === 设备连接 ===
    suspend fun connect(device: BleDevice): Result<ConnectionInfo>
    suspend fun disconnect(): Result<Unit>
    fun isConnected(): Boolean
    fun getCurrentDevice(): BleDevice?
    
    // === WiFi连接管理 ===
    suspend fun connectWiFi(mode: WifiConnectionMode): Result<WifiConnectionInfo>
    fun requestWifiInfo(mode: WifiConnectionMode, isReset: Boolean = true)
    fun getWifiConnectionMode(): WifiConnectionMode
    fun setWifiConnectionMode(mode: WifiConnectionMode)
    
    // === 连接状态 ===
    fun getConnectionStatus(): Connection
    val connectionState: StateFlow<Connection>
    
    // === 事件监听 ===
    fun addConnectionListener(listener: ConnectionListener)
    fun removeConnectionListener(listener: ConnectionListener)
    fun clearAllListeners()
}
```

---

## 消息管理API

### MessagingManager - 消息管理接口

```kotlin
interface MessagingManager {
    // === 导航消息 ===
    fun sendNaviInfo(naviInfo: NaviInfo): Boolean
    fun sendNaviRoute(naviRoute: NaviRoute): Boolean
    fun sendLaneInfo(laneInfo: LaneInfo): Boolean
    fun sendNaviCross(naviCross: NaviCross): Boolean
    fun sendNaviText(naviText: NaviText): Boolean
    fun sendArriveDestination(): Boolean
    
    // === 系统消息 ===
    fun sendNotificationInfo(notificationInfo: NotificationInfo): Boolean
    fun sendWeatherInfo(weatherInfo: WeatherInfo): Boolean
    fun sendGpsSignal(gpsSignal: GpsSignal): Boolean
    
    // === 控制消息 ===
    fun sendNaviStart(): Boolean
    fun sendNaviStop(): Boolean
    fun sendAutoLinkConnect(autoLinkConnect: AutoLinkConnect): Boolean
    
    // === 消息状态 ===
    fun getMessageQueueSize(): Int
    fun clearMessageQueue()
    val queueSize: StateFlow<Int>
    val sendingState: StateFlow<MessageSendingState>
    
    // === 事件监听 ===
    fun addMessageListener(listener: MessageListener)
    fun removeMessageListener(listener: MessageListener)
}
```

---

## 导航管理API

### NavigationManager - 导航管理接口

```kotlin
interface NavigationManager {
    // === 导航模式控制 ===
    suspend fun startNavigation(mode: NaviMode): Result<Unit>
    suspend fun stopNavigation(mode: NaviMode): Result<Unit>
    suspend fun changeNavigationMode(newMode: NaviMode): Result<Unit>
    
    // === 导航状态 ===
    fun getCurrentNavigationMode(): NaviMode
    val navigationState: StateFlow<NavigationState>
    
    // === 事件监听 ===
    fun addNavigationListener(listener: NavigationListener)
    fun removeNavigationListener(listener: NavigationListener)
}
```

---

## 投屏管理API

### ProjectionManager - 投屏管理接口

```kotlin
interface ProjectionManager {
    // === 投屏控制 ===
    fun setMediaProjection(mediaProjection: MediaProjection)
    fun requestLockScreenDisplay()
    suspend fun startMirror(): Result<Unit>
    suspend fun stopMirror(): Result<Unit>
    
    // === 显示管理 ===
    fun getAvailableDisplays(): List<Display>
    val currentDisplay: StateFlow<Display?>
    
    // === 投屏状态 ===
    val projectionState: StateFlow<ProjectionState>
    
    // === 事件监听 ===
    fun addProjectionListener(listener: ProjectionListener)
    fun removeProjectionListener(listener: ProjectionListener)
}
```

---

## 配置管理API

### ConfigManager - 配置管理接口

```kotlin
interface ConfigManager {
    // === 设备配置 ===
    suspend fun getDeviceInfo(): Result<DeviceInfo>
    suspend fun getDeviceConfig(): Result<DeviceConfig>
    
    // === 用户偏好 ===
    suspend fun getUserPreferences(): Result<UserPreferences>
    suspend fun updateUserPreferences(preferences: UserPreferences): Result<Unit>
    
    // === 版本信息 ===
    suspend fun getVersionInfo(): Result<VersionInfo>
    
    // === 配置状态 ===
    val configState: StateFlow<ConfigState>
    
    // === 事件监听 ===
    fun addConfigListener(listener: ConfigListener)
    fun removeConfigListener(listener: ConfigListener)
}
```

---

## 监听器API

### ConnectionListener - 连接事件监听器

```kotlin
interface ConnectionListener {
    // === 扫描事件 ===
    fun onScanStarted() {}
    fun onScanResult(devices: List<BleDevice>) {}
    fun onScanFinished() {}

    // === 连接状态事件 ===
    fun onConnectionStateChanged(status: ConnectionStatus) {}
    fun onDeviceConnected(device: BleDevice, info: ConnectionInfo) {}
    fun onDeviceDisconnected(device: BleDevice, reason: DisconnectReason) {}

    // === WiFi连接事件 ===
    fun onWifiStateChanged(isConnected: Boolean) {}
    fun onWifiConnected(mode: WifiConnectionMode, info: WifiConnectionInfo) {}
    fun onWifiDisconnected(mode: WifiConnectionMode) {}
    fun onWifiConnectionFailed(mode: WifiConnectionMode, error: String) {}

    // === 权限和错误事件 ===
    fun onNeedBluetoothScanPermission() {}
    fun onRequestOpenBluetooth() {}
    fun onNeedLocationPermission() {}
    fun onConnectionError(error: ConnectionError, message: String?) {}
    fun onSignalStrengthChanged(strength: Int) {}
}
```

### MessageListener - 消息事件监听器

```kotlin
interface MessageListener {
    // === 消息发送事件 ===
    fun onMessageSent(messageType: String, success: Boolean) {}
    fun onMessageError(error: String) {}
    fun onQueueSizeChanged(size: Int) {}
    fun onSendingStateChanged(state: MessageSendingState) {}

    // === 接收消息事件 ===
    fun onNaviModeChangeReceived(mode: NaviMode) {}
    fun onConfigChangeReceived(config: RiderServiceConfig) {}
    fun onWeatherInfoRequested() {}
    fun onVersionRequested() {}
}
```

### NavigationListener - 导航事件监听器

```kotlin
interface NavigationListener {
    // === 导航模式事件 ===
    fun onNavigationModeChanged(oldMode: NaviMode, newMode: NaviMode) {}
    fun onNavigationModeChangeResponse(mode: NaviMode, isReady: Boolean) {}
    fun onNavigationStarted(mode: NaviMode) {}
    fun onNavigationStopped(mode: NaviMode) {}

    // === 导航状态事件 ===
    fun onNavigationStateChanged(state: NavigationState) {}
    fun onNavigationError(error: String) {}
}
```

### ProjectionListener - 投屏事件监听器

```kotlin
interface ProjectionListener {
    // === 显示事件 ===
    fun onDisplayInitialized(display: Display) {}
    fun onDisplayReleased(display: Display) {}
    fun onVideoChannelReady() {}

    // === 镜像事件 ===
    fun onRequestMediaProjection() {}
    fun onMirrorStarted() {}
    fun onMirrorStopped() {}

    // === 投屏状态事件 ===
    fun onProjectionStateChanged(state: ProjectionState) {}
    fun onProjectionError(error: String) {}
}
```

### ConfigListener - 配置事件监听器

```kotlin
interface ConfigListener {
    // === 配置变更事件 ===
    fun onConfigChanged(config: RiderServiceConfig) {}
    fun onUserPreferencesChanged(preferences: UserPreferences) {}
    fun onDeviceInfoUpdated(deviceInfo: DeviceInfo) {}

    // === 配置状态事件 ===
    fun onConfigStateChanged(state: ConfigState) {}
    fun onConfigError(error: String) {}
}
```

---

## 数据类型API

### 配置数据类

```kotlin
data class RiderServiceConfig(
    val application: Application,
    val debugMode: Boolean = false,
    val logLevel: LogLevel = LogLevel.INFO,
    val connectionConfig: ConnectionConfig = ConnectionConfig(),
    val messagingConfig: MessagingConfig = MessagingConfig(),
    val navigationConfig: NavigationConfig = NavigationConfig(),
    val projectionConfig: ProjectionConfig = ProjectionConfig(),
    val userPreferences: UserPreferences = UserPreferences()
)

data class ConnectionConfig(
    val wifiMode: WifiConnectionMode = WifiConnectionMode.WIFI_AP_CLIENT,
    val wifiAutoConnect: Boolean = true,
    val wifiConnectionTimeout: Duration = 30.seconds,
    val bleScanTimeout: Duration = 10.seconds,
    val bleConnectionTimeout: Duration = 15.seconds,
    val bleAutoReconnect: Boolean = true,
    val retryPolicy: RetryPolicy = RetryPolicy(),
    val enableConnectionMetrics: Boolean = false
)

data class MessagingConfig(
    val maxQueueSize: Int = 100,
    val messageTimeout: Duration = 5.seconds,
    val enableMessagePersistence: Boolean = false,
    val batchSending: Boolean = false,
    val batchSize: Int = 10,
    val batchInterval: Duration = 100.milliseconds,
    val enablePriorityQueue: Boolean = true,
    val highPriorityTypes: Set<String> = setOf("NaviInfo", "GpsSignal"),
    val enableCompression: Boolean = false,
    val compressionThreshold: Int = 1024
)

data class NavigationConfig(
    val defaultMode: NaviMode = NaviMode.SimpleNavi,
    val autoStartNavigation: Boolean = false,
    val allowModeSwitch: Boolean = true,
    val modeSwitchTimeout: Duration = 10.seconds,
    val naviUpdateInterval: Duration = 1.seconds,
    val enableRouteOptimization: Boolean = true,
    val enableVoiceGuidance: Boolean = true,
    val enableLaneGuidance: Boolean = true,
    val enableSpeedLimit: Boolean = true,
    val enableTrafficInfo: Boolean = true,
    val mapOrientation: MapOrientation = MapOrientation.NORTH_UP,
    val zoomLevel: Int = 15,
    val enableNightMode: Boolean = false
)

data class ProjectionConfig(
    val enableAutoProjection: Boolean = false,
    val projectionQuality: ProjectionQuality = ProjectionQuality.MEDIUM,
    val frameRate: Int = 30,
    val resolution: Resolution = Resolution.HD_720P,
    val enableMirrorMode: Boolean = true,
    val mirrorOrientation: MirrorOrientation = MirrorOrientation.AUTO,
    val enableAudioMirror: Boolean = false,
    val enableHardwareAcceleration: Boolean = true,
    val bufferSize: Int = 1024 * 1024,
    val maxBitrate: Int = 8000,
    val enableAdaptiveBitrate: Boolean = true,
    val brightness: Float = 1.0f,
    val contrast: Float = 1.0f,
    val saturation: Float = 1.0f,
    val enableTouchInput: Boolean = true,
    val enableGestureControl: Boolean = false,
    val touchSensitivity: Float = 1.0f
)

data class UserPreferences(
    val autoStartNavigation: Boolean = false,
    val defaultNavigationMode: NaviMode = NaviMode.SimpleNavi,
    val preferredMapStyle: MapStyle = MapStyle.STANDARD,
    val enableNotifications: Boolean = true,
    val notificationTypes: Set<NotificationType> = setOf(
        NotificationType.NAVIGATION,
        NotificationType.TRAFFIC,
        NotificationType.WEATHER
    ),
    val notificationSound: Boolean = true,
    val notificationVibration: Boolean = true,
    val theme: Theme = Theme.AUTO,
    val fontSize: FontSize = FontSize.MEDIUM,
    val language: String = "zh-CN",
    val units: Units = Units.METRIC,
    val preferredWifiMode: WifiConnectionMode = WifiConnectionMode.WIFI_AP_CLIENT,
    val autoConnectLastDevice: Boolean = true,
    val connectionTimeout: Duration = 30.seconds,
    val enableAnalytics: Boolean = true,
    val enableCrashReporting: Boolean = true,
    val enableLocationSharing: Boolean = false,
    val enablePowerSaving: Boolean = false,
    val backgroundDataUsage: DataUsageLevel = DataUsageLevel.NORMAL,
    val cacheSize: Int = 100
)

data class RetryPolicy(
    val maxRetries: Int = 3,
    val initialDelayMs: Long = 1000,
    val maxDelayMs: Long = 10000,
    val backoffMultiplier: Double = 2.0,
    val enableJitter: Boolean = true
)
```

### 状态数据类

```kotlin
data class NavigationState(
    val currentMode: NaviMode,
    val isNavigating: Boolean,
    val lastUpdateTime: Long
)

data class ProjectionState(
    val isProjecting: Boolean,
    val projectionMode: ProjectionMode,
    val display: Display?
)

data class ConfigState(
    val isLoaded: Boolean,
    val deviceInfo: DeviceInfo?,
    val userPreferences: UserPreferences?
)

data class ConnectionInfo(
    val device: BleDevice,
    val connectionTime: Long,
    val signalStrength: Int,
    val protocolVersion: String = "1.0"
)

data class WifiConnectionInfo(
    val mode: WifiConnectionMode,
    val ssid: String = "",
    val ipAddress: String = "",
    val peerAddress: String = "",
    val timestamp: Long = System.currentTimeMillis()
)
```

### 消息数据类（保持现有结构）

```kotlin
// 导航消息
data class NaviInfo(...)
data class NaviRoute(...)
data class LaneInfo(...)
data class NaviCross(...)
data class NaviText(...)

// 系统消息
data class NotificationInfo(...)
data class WeatherInfo(...)
data class GpsSignal(...)

// 控制消息
data class AutoLinkConnect(...)
```

---

## 枚举类型API

### 基础枚举

```kotlin
enum class LogLevel {
    VERBOSE, DEBUG, INFO, WARN, ERROR, NONE
}

enum class ServiceState {
    UNINITIALIZED, INITIALIZING, INITIALIZED, ERROR, DESTROYED
}

enum class MessageSendingState {
    IDLE, SENDING, PAUSED, ERROR
}

enum class ConnectionStatus {
    DISCONNECTED, SCANNING, CONNECTING, CONNECTED, RECONNECTING, ERROR
}

enum class ConnectionError {
    SCAN_FAILED, CONNECTION_FAILED, PERMISSION_DENIED,
    BLUETOOTH_DISABLED, WIFI_DISABLED, TIMEOUT, UNKNOWN
}

enum class DisconnectReason {
    USER_REQUESTED, CONNECTION_LOST, DEVICE_NOT_FOUND,
    PERMISSION_DENIED, TIMEOUT, UNKNOWN
}
```

### WiFi连接枚举

```kotlin
enum class WifiConnectionMode {
    WIFI_AP_CLIENT, WIFI_P2P
}
```

### 导航相关枚举

```kotlin
enum class NaviMode {
    NoNavi, SimpleNavi, ScreenNavi, CruiseNAVI, MirrorNAVI
}

enum class MapOrientation {
    NORTH_UP, HEADING_UP, COURSE_UP
}

enum class MapStyle {
    STANDARD, SATELLITE, TERRAIN, HYBRID
}
```

### 投屏相关枚举

```kotlin
enum class ProjectionQuality {
    LOW, MEDIUM, HIGH, ULTRA
}

enum class Resolution {
    HD_720P, FHD_1080P, QHD_1440P, UHD_4K
}

enum class MirrorOrientation {
    AUTO, PORTRAIT, LANDSCAPE, REVERSE_PORTRAIT, REVERSE_LANDSCAPE
}

enum class ProjectionMode {
    NONE, MIRROR, PRESENTATION, LOCK_SCREEN
}
```

### 用户偏好相关枚举

```kotlin
enum class NotificationType {
    NAVIGATION, TRAFFIC, WEATHER, SYSTEM, MESSAGE
}

enum class Theme {
    LIGHT, DARK, AUTO
}

enum class FontSize {
    SMALL, MEDIUM, LARGE, EXTRA_LARGE
}

enum class Units {
    METRIC, IMPERIAL
}

enum class DataUsageLevel {
    LOW, NORMAL, HIGH, UNLIMITED
}
```

---

## 使用示例

### 基础使用

```kotlin
// 默认配置
val riderService = RiderServiceBuilder.createDefault(application)

// 自定义配置
val riderService = RiderServiceBuilder.newBuilder()
    .application(application)
    .debugMode(BuildConfig.DEBUG)
    .logLevel(LogLevel.DEBUG)
    .connection {
        wifiMode(WifiConnectionMode.WIFI_AP_CLIENT)
        autoReconnect(true)
        wifiTimeout(30.seconds)
    }
    .messaging {
        queueSize(200)
        enablePriorityQueue(true)
        batchSending(enabled = true, batchSize = 5)
    }
    .navigation {
        defaultMode(NaviMode.SimpleNavi)
        enableVoiceGuidance(true)
        mapOrientation(MapOrientation.HEADING_UP)
    }
    .projection {
        quality(ProjectionQuality.HIGH)
        frameRate(60)
        resolution(Resolution.FHD_1080P)
    }
    .userPreferences {
        autoStartNavigation(false)
        preferredMapStyle(MapStyle.SATELLITE)
        theme(Theme.DARK)
    }
    .build()
```

### 连接和消息发送

```kotlin
// 扫描和连接
lifecycleScope.launch {
    val scanResult = riderService.connection.startBleScan(10000)
    if (scanResult.isSuccess) {
        val devices = scanResult.getOrNull() ?: emptyList()
        if (devices.isNotEmpty()) {
            val connectResult = riderService.connection.connect(devices.first())
            if (connectResult.isSuccess) {
                // 连接成功，发送消息
                val naviInfo = NaviInfo(...)
                riderService.messaging.sendNaviInfo(naviInfo)
            }
        }
    }
}
```

### 监听器注册

```kotlin
// 注册监听器
riderService.connection.addConnectionListener(object : ConnectionListener {
    override fun onConnectionStateChanged(status: ConnectionStatus) {
        // 处理连接状态变化
    }

    override fun onDeviceConnected(device: BleDevice, info: ConnectionInfo) {
        // 处理设备连接成功
    }
})

riderService.messaging.addMessageListener(object : MessageListener {
    override fun onMessageSent(messageType: String, success: Boolean) {
        // 处理消息发送结果
    }
})
```

---

## API总结

RiderService SDK 基于Builder模式提供了完整的API体系：

### 🏗️ **Builder API** (7个类)
- 1个主构建器 + 5个子配置构建器 + 1个快速创建方法

### 🔧 **核心服务API** (6个接口)
- 1个主服务接口 + 5个管理器接口

### 📡 **连接管理API** (12个方法)
- 设备扫描、连接、WiFi管理、状态监控

### 💬 **消息管理API** (14个方法)
- 导航消息、系统消息、控制消息、状态管理

### 🧭 **导航管理API** (6个方法)
- 模式控制、状态监控

### 📺 **投屏管理API** (8个方法)
- 投屏控制、显示管理、状态监控

### ⚙️ **配置管理API** (7个方法)
- 设备配置、用户偏好、版本信息

### 👂 **监听器API** (5个接口，35个回调方法)
- 连接、消息、导航、投屏、配置事件监听

### 📊 **数据类型API** (20+个数据类)
- 配置类、状态类、消息类

### 🏷️ **枚举类型API** (15个枚举)
- 基础枚举、连接枚举、导航枚举、投屏枚举、用户偏好枚举

**总计**: 100+ 个公开API，提供完整的RiderService SDK功能。
