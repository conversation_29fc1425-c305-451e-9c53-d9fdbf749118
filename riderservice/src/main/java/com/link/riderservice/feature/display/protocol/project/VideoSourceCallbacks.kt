package com.link.riderservice.feature.display.protocol.project

/**
 * <AUTHOR>
 * @date 2017/9/19
 * @desc Java perspective of the `IVideoSourceCallbacks` native interface.
 */
interface VideoSourceCallbacks : NativeCallback {
    /**
     * onChannelOpened
     *
     * @return 0 or -1
     */
    fun onChannelOpened(): Int

    /**
     * configCallback
     *
     * @param status   status
     * @param maxUnack maxUnack
     * @param prefer   prefer
     * @param size     size
     * @return 0 or -1
     */
    fun configCallback(status: Int, maxUnack: Int, prefer: IntArray, size: Int): Int

    /**
     * ackCallback
     *
     * @param sessionId sessionId
     * @param numFrames numFrames
     * @return 0 or -1
     */
    fun ackCallback(sessionId: Int, numFrames: Int): Int

    /**
     * videoFocusNotifCallback
     *
     * @param focus       focus
     * @param unsolicited unsolicited
     * @return 0 or -1
     */
    fun videoFocusNotifCallback(focus: Int, unsolicited: Boolean): Int

    /**
     * displayChangeCallback
     *
     * @param width       width
     * @param height      height
     * @param isLandscape isLandscape
     * @param density     density
     * @return 0 or -1
     */
    fun displayChangeCallback(width: Int, height: Int, isLandscape: Boolean, density: Int): Int

    /**
     * discoverVideoConfigCallback
     *
     * @param codec codec
     * @param fps   fps
     * @param w     w
     * @param h     h
     * @return success or not
     */
    fun discoverVideoConfigCallback(codec: Int, fps: Int, w: Int, h: Int): Boolean

    /**
     * startResponseCallback
     *
     * @param isOK isOK
     * @return 0 or -1
     */
    fun startResponseCallback(isOK: Boolean): Int

    /**
     * stopResponseCallback
     *
     * @return 0 or -1
     */
    fun stopResponseCallback(): Int
}