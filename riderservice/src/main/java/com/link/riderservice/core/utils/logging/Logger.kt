package com.link.riderservice.core.utils.logging

/**
 * 统一日志接口
 */
internal interface Logger {
    
    /**
     * 记录详细信息
     */
    fun v(tag: String, message: String)
    
    /**
     * 记录详细信息（带异常）
     */
    fun v(tag: String, message: String, throwable: Throwable?)
    
    /**
     * 记录调试信息
     */
    fun d(tag: String, message: String)
    
    /**
     * 记录调试信息（带异常）
     */
    fun d(tag: String, message: String, throwable: Throwable?)
    
    /**
     * 记录一般信息
     */
    fun i(tag: String, message: String)
    
    /**
     * 记录一般信息（带异常）
     */
    fun i(tag: String, message: String, throwable: Throwable?)
    
    /**
     * 记录警告信息
     */
    fun w(tag: String, message: String)
    
    /**
     * 记录警告信息（带异常）
     */
    fun w(tag: String, message: String, throwable: Throwable?)
    
    /**
     * 记录错误信息
     */
    fun e(tag: String, message: String)
    
    /**
     * 记录错误信息（带异常）
     */
    fun e(tag: String, message: String, throwable: Throwable?)
    
    /**
     * 判断是否启用指定级别的日志
     */
    fun isLoggable(level: LogLevel): Boolean
} 