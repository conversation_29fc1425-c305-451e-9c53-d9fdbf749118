// Copyright 2014 Google Inc. All Rights Reserved.

#include "util/common.h"
#include "ChannelManager.h"
#include "Controller.h"
#include "MessageRouter.h"
#include "ProtocolEndpointBase.h"

bool MessageRouter::init(ChannelManager *channelManager) {
    mChannelManager = channelManager;
    memset(mChannelServiceIdMap, SENTINEL_SERVICE_ID, sizeof(mChannelServiceIdMap));
    memset(mServiceMap, 0, sizeof(mServiceMap));
    return true;
}

void MessageRouter::shutdown() {
    mChannelManager = nullptr;
}

void MessageRouter::setupMapping(uint8_t serviceId, uint8_t channelId) {
    mChannelServiceIdMap[channelId] = serviceId;
}

uint16_t MessageRouter::extractType(uint8_t *message) {
    uint16_t type = (uint16_t) *message++ << 8;
    type |= *message;
    return type;
}

int MessageRouter::routeMessage(uint8_t channelId, const shared_ptr<IoBuffer> &message) {
    uint8_t serviceId = mChannelServiceIdMap[channelId];
    if (serviceId == SENTINEL_SERVICE_ID) {
        return STATUS_INVALID_CHANNEL;
    }

    ProtocolEndpointBase *service = mServiceMap[serviceId];
    if (service == nullptr) {
        return STATUS_INVALID_SERVICE;
    }

    int status;
    if (service->isPassthrough()) {
        status = service->handleRawMessage(channelId, message);
    } else {
        uint16_t type = extractType((uint8_t *) message->raw());
        status = service->routeMessage(channelId, type, message);
    }
    if (status == STATUS_UNEXPECTED_MESSAGE) {
        status = sendUnexpectedMessage(channelId);
    }

    return status;
}

bool MessageRouter::registerService(ProtocolEndpointBase *service) {
    if (service->id() >= SENTINEL_SERVICE_ID) {
        return false;
    }
//    if (mServiceMap[service->id()] != NULL) {
//        return false;
//    }
    mServiceMap[service->id()] = service;
    return true;
}

int MessageRouter::queueOutgoing(uint8_t channelId, void *buf, size_t len) {
    return mChannelManager->queueOutgoing(channelId, false, buf, len);
}

int MessageRouter::queueOutgoingUnencrypted(uint8_t channelId, void *buf, size_t len) {
    return mChannelManager->queueOutgoingUnencrypted(channelId, false, buf, len);
}

void MessageRouter::discoverServices(ServiceDiscoveryResponse &sdr) {
    int num = sdr.services_size();
    for (int i = 0; i < num; i++) {
        for (int j = 1; j < MAX_SERVICES; j++) {
            if (mServiceMap[j] != nullptr) {
                ProtocolEndpointBase *endpoint = mServiceMap[j];
                const Service &srv = sdr.services(i);
                if (endpoint->discoverService(srv)) {
                    int channelId = mChannelManager->allocChannel(srv.id(), endpoint->priority());
                    if (channelId < MAX_CHANNELS) {
                        //LOGE("channelId:%d,id:%d",channelId,srv.id());
                        setupMapping(j, channelId);
                        sendChannelOpenRequest(srv.id(), channelId, endpoint->priority());
                        break;
                    } else {
                        LOGE("no available channel!\n");
                    }
                }
            }
        }
    }
}

void MessageRouter::marshallProto(uint16_t type, const google::protobuf::MessageLite &proto,
                                  IoBuffer *out) {
    size_t len = proto.ByteSize();
    out->resize(len + sizeof(uint16_t));
    auto *ptr = (uint8_t *) out->raw();
    WRITE_BE16(ptr, type);
    proto.SerializeToArray(ptr + sizeof(uint16_t), len);
}

void MessageRouter::unrecoverableError(MessageStatus err) {
    auto *controller = (Controller *) mServiceMap[CONTROLLER_SERVICE_ID];
    controller->unrecoverableError(err);
}

bool MessageRouter::closeChannel(uint8_t channelId) {
    if (mChannelServiceIdMap[channelId] != SENTINEL_SERVICE_ID) {
        mChannelServiceIdMap[channelId] = SENTINEL_SERVICE_ID;
        sendChannelCloseNotifi(channelId);
        return true;
    }
    return false;
}

bool MessageRouter::forceCloseChannel(uint8_t channelId) {
    if (mChannelServiceIdMap[channelId] == SENTINEL_SERVICE_ID) {
        return false;
    }

    ProtocolEndpointBase *service = mServiceMap[mChannelServiceIdMap[channelId]];
    if (service == nullptr) {
        return false;
    }
    if (!service->onChannelClosed(channelId)) {
        return false;
    }
    mChannelServiceIdMap[channelId] = SENTINEL_SERVICE_ID;
    mChannelManager->freeChannel(channelId);
    return true;
}

bool MessageRouter::isChannelClosed(uint8_t channelId) {
    return !(channelId == 0 || mChannelServiceIdMap[channelId] != SENTINEL_SERVICE_ID);
}

int MessageRouter::handleChannelOpenResponse(uint8_t channelId, MessageStatus status,
                                             uint32_t extraMessage) {
    uint8_t serviceId = mChannelServiceIdMap[channelId];
    if (serviceId >= SENTINEL_SERVICE_ID) {
        return STATUS_INVALID_CHANNEL;
    }
    if (status != STATUS_SUCCESS) {
        setupMapping(SENTINEL_SERVICE_ID, channelId);
        mChannelManager->freeChannel(channelId);
    } else {
        ProtocolEndpointBase *service = mServiceMap[serviceId];
        if (service != nullptr) {
            service->onChannelOpened(channelId, extraMessage);
        }
    }
    return STATUS_SUCCESS;
}

int MessageRouter::handleChannelCloseNotif(uint8_t channelId,
                                           const ChannelCloseNotification &notification) {
    if (mChannelServiceIdMap[channelId] == SENTINEL_SERVICE_ID) {
        return STATUS_INVALID_CHANNEL;
    }

    ProtocolEndpointBase *service = mServiceMap[mChannelServiceIdMap[channelId]];
    if (service == nullptr) {
        return STATUS_INVALID_SERVICE;
    }
    if (!service->onChannelClosed(channelId)) {
        return STATUS_INTERNAL_ERROR;
    }
    mChannelServiceIdMap[channelId] = SENTINEL_SERVICE_ID;
    mChannelManager->freeChannel(channelId);
    return STATUS_SUCCESS;
}

int MessageRouter::sendChannelOpenRequest(uint8_t serviceId, uint8_t channelId, uint8_t priority) {
    LOG("[CHANNEL][%d] -> OpenRequest\n", channelId);
    ChannelOpenRequest req;
    req.set_service_id(serviceId);
    req.set_priority(priority);
    IoBuffer buf;
    marshallProto(MESSAGE_CHANNEL_OPEN_REQUEST, req, &buf);
    return mChannelManager->queueOutgoing(channelId, true, buf.raw(), buf.size());
}

int MessageRouter::sendChannelCloseNotifi(uint8_t channelId) {
    LOG("[CHANNEL][%d] -> CloseNotification\n", channelId);
    ChannelCloseNotification notification;
    IoBuffer buf;
    marshallProto(MESSAGE_CHANNEL_CLOSE_NOTIFICATION, notification, &buf);
    return mChannelManager->queueOutgoing(channelId, true, buf.raw(), buf.size());
}

int MessageRouter::sendUnexpectedMessage(uint8_t channelId) {
    uint16_t buf;
    LOGW("Sending unexpected message on channel %d", channelId);
    WRITE_BE16(&buf, MESSAGE_UNEXPECTED_MESSAGE);
    return mChannelManager->queueOutgoing(channelId, false, &buf, sizeof(uint16_t));
}

int MessageRouter::routeChannelControlMsg(const shared_ptr<Frame> &frame,
                                          void *message, size_t len) {
    uint16_t type = extractType((uint8_t *) message);
    int status = STATUS_INVALID_CHANNEL;
    uint8_t *ptr = (uint8_t *) message + sizeof(uint16_t);
    len -= sizeof(uint16_t);
    LOG("[CHANNEL][%d] <- ", frame->channelId);

    switch (type) {
        case MESSAGE_CHANNEL_OPEN_RESPONSE: {
            LOG("OpenResponse\n");
            ChannelOpenResponse response;
            if (PARSE_PROTO(response, ptr, len)) {
                if (response.has_message_existance()) {
                    status = handleChannelOpenResponse(frame->channelId, response.status(),
                                                       response.message_existance());
                } else {
                    status = handleChannelOpenResponse(frame->channelId, response.status());
                }
            }
            break;
        }
        case MESSAGE_CHANNEL_CLOSE_NOTIFICATION: {
            LOG("CloseNotification\n");
            ChannelCloseNotification notification;
            if (!PARSE_PROTO(notification, ptr, len)) {
                status = handleChannelCloseNotif(frame->channelId, notification);
            }
            break;
        }
        default: {
            status = sendUnexpectedMessage(frame->channelId);
            assert(status == STATUS_SUCCESS);
            break;
        }
    }
    return status;
}


