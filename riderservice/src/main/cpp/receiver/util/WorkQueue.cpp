// Copyright 2014 Google Inc. All Rights Reserved.
#include "stdio.h"
#include "WorkQueue.h"
#include "Log.h"

#define MAX_WQ_THREAD_NAME_SIZE     32

void WorkQueueThread::run() {

    while (!mQueue->mIsStopping) {
        shared_ptr<WorkItem> item = mQueue->dequeueWork();
        if (item == nullptr || mQueue->mIsStopping) {
            // Happens while shutting down.
            break;
        }
        item->run();
        yield();
    }
}

WorkQueue::~WorkQueue() {
    if (mThreads != nullptr) {
        LOGW("Workqueue went out of scope without calling shutdown!");
    }
}

bool WorkQueue::start() {
    mThreads = new WorkQueueThread[mNumThreads];

    bool ret = true;
    for (int i = 0; i < mNumThreads; i++) {
        // Slightly idiotic that c++ has no good way to call the parameterized
        // constructor while allocating an array of objects.
        mThreads[i].setQueue(this);
        ret &= mThreads[i].start();
    }
    return ret;
}

void WorkQueue::queueWork(const shared_ptr<WorkItem> &work) {
    {
        // Keep the sem-up outside the scope of mLock to prevent unnecessary lock nesting.
        Autolock l(&mLock);
        mWorkItems.push_back(work);
    }
    mAvailable.up();
}

shared_ptr<WorkItem> WorkQueue::dequeueWork() {
    mAvailable.down();
    Autolock l(&mLock);
    if (mWorkItems.empty()) {
        return nullptr;
    }
    shared_ptr<WorkItem> work = mWorkItems.front();
    mWorkItems.pop_front();
    return work;
}

void WorkQueue::shutdown() {
    mIsStopping = true;
    for (int i = 0; i < mNumThreads; i++) {
        mAvailable.up();
    }
    for (int i = 0; i < mNumThreads; i++) {
        mThreads[i].join();
    }
    delete[] mThreads;
    mThreads = nullptr;
}

void WorkQueue::setName(const char *name) {
    char str[MAX_WQ_THREAD_NAME_SIZE];
    for (int i = 0; i < mNumThreads; i++) {
        sprintf(str, "%s-%d", name, i);
        mThreads[i].setName(str);
    }
}
