package com.link.riderservice.api.dto

import android.bluetooth.BluetoothDevice

/**
 * RiderService 连接状态
 * @property wifiStatus wifi 连接状态
 * @property btStatus 蓝牙连接状态
 * @see WifiStatus
 * @see BleStatus
 */
data class Connection(
    val wifiStatus: WifiStatus = WifiStatus.IDLE,
    val btStatus: BleStatus = BleStatus.IDLE
)

/**
 * 蓝牙连接状态
 */
sealed class BleStatus {
    /**
     * 空闲状态
     */
    object IDLE : BleStatus()

    /**
     * 正在连接
     * @param device 蓝牙设备
     */
    data class DeviceConnecting(val device: BluetoothDevice) : BleStatus()

    /**
     * 已连接
     * @param device 蓝牙设备
     */
    data class DeviceConnected(val device: BluetoothDevice) : BleStatus()

    /**
     * 连接失败
     * @param reason 连接失败的原因
     */
    data class DeviceFailedToConnect(val device: BluetoothDevice, val reason: Int) : BleStatus()

    /**
     * 已断开
     * @param device 蓝牙设备
     * @param reason 连接失败的原因
     */
    data class DeviceDisconnected(val device: BluetoothDevice, val reason: Int) : BleStatus()
}

/**
 * wifi 连接状态
 */
sealed class WifiStatus {
    /**
     * 空闲状态
     */
    object IDLE : WifiStatus()

    /**
     * 已连接
     */
    object DeviceConnected : WifiStatus()

    /**
     * 已断开
     */
    object DeviceDisconnected : WifiStatus()
}
